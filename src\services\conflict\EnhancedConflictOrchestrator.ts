import { Logger } from 'winston';
import { ConflictInfo } from '../../types/conflict/ConflictTypes.js';
import { ResourceInfo } from '../../types/resource/interfaces.js';
import { DatabaseService } from '../database/DatabaseService.js';
import { CRC32ConflictDetector } from './detectors/CRC32ConflictDetector.js';
import { TGIConflictDetector } from './detectors/TGIConflictDetector.js';
import { Sims4ConflictFilter } from './filters/Sims4ConflictFilter.js';

/**
 * Configuration for enhanced conflict detection
 */
export interface EnhancedConflictDetectionConfig {
    /** Enable TGI-based conflict detection (recommended: true) */
    enableTGIDetection?: boolean;

    /** Enable CRC32-based duplicate detection (recommended: true) */
    enableCRC32Detection?: boolean;

    /** Enable Sims 4-specific filtering (recommended: true) */
    enableSims4Filtering?: boolean;

    /** CRC32 buffer size for partial file reading (recommended: 2048) */
    crc32BufferSize?: number;

    /** Maximum conflicts to return (to prevent overwhelming users) */
    maxConflictsToReturn?: number;

    /** Enable detailed logging */
    enableDetailedLogging?: boolean;
}

/**
 * Enhanced conflict detection orchestrator that combines multiple detection methods
 * and applies Sims 4-specific filtering to reduce false positives
 */
export class EnhancedConflictOrchestrator {
    private logger: Logger;
    private databaseService: DatabaseService;
    private tgiDetector: TGIConflictDetector;
    private crc32Detector: CRC32ConflictDetector;
    private sims4Filter: Sims4ConflictFilter;
    private config: EnhancedConflictDetectionConfig;

    constructor(
        databaseService: DatabaseService,
        config: EnhancedConflictDetectionConfig = {},
        logger?: Logger
    ) {
        this.databaseService = databaseService;
        this.logger = logger || console as any;

        // Set default configuration (removed ...config spread to prevent LSH properties)
        this.config = {
            enableTGIDetection: config.enableTGIDetection !== false,
            enableCRC32Detection: config.enableCRC32Detection !== false,
            enableSims4Filtering: config.enableSims4Filtering !== false,
            crc32BufferSize: config.crc32BufferSize || 2048,
            maxConflictsToReturn: config.maxConflictsToReturn || 1000,
            enableDetailedLogging: config.enableDetailedLogging || false
        };

        // Initialize detectors
        this.tgiDetector = new TGIConflictDetector(
            databaseService,
            {
                enabled: this.config.enableTGIDetection,
                detectExactMatches: true,
                detectPartialMatches: true,
                detectGroupConflicts: false, // Disabled to reduce false positives
                detectInstanceConflicts: true
            },
            logger
        );

        this.crc32Detector = new CRC32ConflictDetector(
            databaseService,
            {
                enabled: this.config.enableCRC32Detection,
                bufferSize: this.config.crc32BufferSize,
                maxFileSize: 150 * 1024 * 1024, // 150MB
                enableDetailedLogging: this.config.enableDetailedLogging,
                enableCaching: true
            },
            logger
        );

        this.sims4Filter = new Sims4ConflictFilter(logger);

        this.logger.info('EnhancedConflictOrchestrator initialized with config:', this.config);
    }

    /**
     * Detect conflicts among multiple resources using enhanced detection and filtering
     * @param resources Array of resources to analyze
     * @returns Array of filtered conflicts
     */
    public async detectConflicts(resources: ResourceInfo[]): Promise<ConflictInfo[]> {
        const startTime = Date.now();
        this.logger.info(`Starting enhanced conflict detection for ${resources.length} resources`);

        const allConflicts: ConflictInfo[] = [];
        const resourceMap = this.createResourceMap(resources);

        // Phase 1: TGI-based detection (most reliable)
        if (this.config.enableTGIDetection) {
            const tgiConflicts = await this.detectTGIConflicts(resources);
            allConflicts.push(...tgiConflicts);
            this.logger.info(`TGI detection found ${tgiConflicts.length} conflicts`);
        }

        // Phase 2: CRC32-based duplicate detection (for exact duplicates)
        if (this.config.enableCRC32Detection) {
            const crc32Conflicts = await this.detectCRC32Conflicts(resources);
            allConflicts.push(...crc32Conflicts);
            this.logger.info(`CRC32 detection found ${crc32Conflicts.length} conflicts`);
        }

        // Phase 3: Remove duplicates
        const uniqueConflicts = this.removeDuplicateConflicts(allConflicts);
        this.logger.info(`After deduplication: ${uniqueConflicts.length} conflicts`);

        // Phase 4: Apply Sims 4-specific filtering
        let filteredConflicts = uniqueConflicts;
        if (this.config.enableSims4Filtering) {
            filteredConflicts = this.sims4Filter.filterConflicts(uniqueConflicts, resourceMap);
            this.logger.info(`After Sims 4 filtering: ${filteredConflicts.length} conflicts`);
        }

        // Phase 5: Limit results to prevent overwhelming users
        if (filteredConflicts.length > this.config.maxConflictsToReturn!) {
            // Sort by severity and confidence, keep the most important ones
            filteredConflicts = this.prioritizeConflicts(filteredConflicts)
                .slice(0, this.config.maxConflictsToReturn!);

            this.logger.warn(`Limited conflicts to ${this.config.maxConflictsToReturn} most important ones`);
        }

        const duration = Date.now() - startTime;
        this.logger.info(`Enhanced conflict detection completed in ${duration}ms. Found ${filteredConflicts.length} relevant conflicts.`);

        return filteredConflicts;
    }

    /**
     * Detect TGI-based conflicts
     */
    private async detectTGIConflicts(resources: ResourceInfo[]): Promise<ConflictInfo[]> {
        const conflicts: ConflictInfo[] = [];

        // Use the TGI detector's batch detection method if available
        if (typeof this.tgiDetector.detectConflicts === 'function') {
            // Check if it accepts an array of resources
            try {
                const result = await this.tgiDetector.detectConflicts(resources as any);
                if (Array.isArray(result)) {
                    return result;
                }
            } catch (error) {
                // Fall back to pairwise detection
            }
        }

        // Pairwise detection fallback
        for (let i = 0; i < resources.length; i++) {
            for (let j = i + 1; j < resources.length; j++) {
                try {
                    const conflict = this.tgiDetector.detectConflict(resources[i], resources[j]);
                    if (conflict) {
                        conflicts.push(conflict);
                    }
                } catch (error) {
                    if (this.config.enableDetailedLogging) {
                        this.logger.debug(`Error in TGI detection: ${error}`);
                    }
                }
            }
        }

        return conflicts;
    }

    /**
     * Detect CRC32-based conflicts (duplicates)
     */
    private async detectCRC32Conflicts(resources: ResourceInfo[]): Promise<ConflictInfo[]> {
        try {
            // Use batch detection for better performance
            return await this.crc32Detector.detectConflicts(resources);
        } catch (error) {
            if (this.config.enableDetailedLogging) {
                this.logger.debug(`Error in CRC32 detection: ${error}`);
            }
            return [];
        }
    }

    /**
     * Create a map of resource ID to resource info for efficient lookup
     */
    private createResourceMap(resources: ResourceInfo[]): Map<string, ResourceInfo> {
        const map = new Map<string, ResourceInfo>();

        for (const resource of resources) {
            if (resource.id) {
                map.set(resource.id, resource);
            }

            // Also add by TGI key for conflict analysis
            const tgiKey = `${resource.type.toString(16)}_${resource.group.toString(16)}_${resource.instance.toString(16)}`;
            map.set(tgiKey, resource);
        }

        return map;
    }

    /**
     * Remove duplicate conflicts based on affected resources
     */
    private removeDuplicateConflicts(conflicts: ConflictInfo[]): ConflictInfo[] {
        const seen = new Set<string>();
        const unique: ConflictInfo[] = [];

        for (const conflict of conflicts) {
            // Create a key based on affected resources
            const resourceKeys = conflict.affectedResources
                ?.map(r => `${r.type}_${r.group}_${r.instance}`)
                .sort()
                .join('|') || '';

            const key = `${conflict.type}_${resourceKeys}`;

            if (!seen.has(key)) {
                seen.add(key);
                unique.push(conflict);
            }
        }

        return unique;
    }

    /**
     * Prioritize conflicts by severity and confidence
     */
    private prioritizeConflicts(conflicts: ConflictInfo[]): ConflictInfo[] {
        return conflicts.sort((a, b) => {
            // Sort by severity first (higher severity first)
            const severityOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1, NONE: 0, UNKNOWN: 0 };
            const severityDiff = (severityOrder[b.severity] || 0) - (severityOrder[a.severity] || 0);

            if (severityDiff !== 0) {
                return severityDiff;
            }

            // Then by confidence (higher confidence first)
            const confidenceA = a.confidence || 0;
            const confidenceB = b.confidence || 0;

            return confidenceB - confidenceA;
        });
    }
}
