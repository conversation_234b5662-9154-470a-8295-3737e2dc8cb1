/**
 * Validator
 *
 * This module provides a comprehensive validation framework for the application.
 * It helps ensure that inputs are valid and provides consistent error handling.
 *
 * Features:
 * - Type validation
 * - Range validation
 * - Format validation
 * - Existence validation
 * - Integrity validation
 * - Consistency validation
 * - Relationship validation
 */

import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { Logger } from '../logging/logger.js';
import { AppError, ErrorCategory, ErrorCode, ErrorSeverity } from '../error/errorTypes.js';

// Define a NONE error code if it doesn't exist
if (!ErrorCode.NONE) {
    (ErrorCode as any).NONE = 0;
}
import { EnhancedErrorHandler } from '../error/enhancedErrorHandler.js';

// Promisify fs functions
const fsAccess = promisify(fs.access);
const fsStat = promisify(fs.stat);

// Create a logger for this module
const logger = new Logger('Validator');

// Get error handler instance
const errorHandler = EnhancedErrorHandler.getInstance();

/**
 * Validation levels
 */
export enum ValidationLevel {
    CRITICAL = 'critical',
    ERROR = 'error',
    WARNING = 'warning',
    INFO = 'info'
}

/**
 * Validation result
 */
export interface ValidationResult {
    valid: boolean;
    level: ValidationLevel;
    message: string;
    code: ErrorCode;
    details?: Record<string, any>;
}

/**
 * Validation options
 */
export interface ValidationOptions {
    level?: ValidationLevel;
    throwOnFailure?: boolean;
    context?: Record<string, any>;
}

/**
 * Validator class
 */
export class Validator {
    private static instance: Validator;

    /**
     * Create a new validator
     */
    private constructor() {
        logger.info('Validator initialized');
    }

    /**
     * Get the validator instance
     * @returns Validator instance
     */
    public static getInstance(): Validator {
        if (!Validator.instance) {
            Validator.instance = new Validator();
        }

        return Validator.instance;
    }

    /**
     * Validate a value
     * @param value Value to validate
     * @param validationFn Validation function
     * @param options Validation options
     * @returns Validation result
     */
    public validate(
        value: any,
        validationFn: (value: any) => ValidationResult,
        options: ValidationOptions = {}
    ): ValidationResult {
        const result = validationFn(value);

        // Override level if specified
        if (options.level) {
            result.level = options.level;
        }

        // Log validation result
        this.logValidationResult(result, options.context);

        // Throw error if requested and validation failed
        if (options.throwOnFailure && !result.valid) {
            throw errorHandler.createError(
                result.code,
                result.message,
                {
                    category: ErrorCategory.VALIDATION,
                    severity: this.levelToSeverity(result.level),
                    context: {
                        component: 'Validator',
                        operation: 'validate',
                        parameters: { value },
                        ...options.context
                    },
                    userMessage: {
                        summary: result.message,
                        details: result.details ? JSON.stringify(result.details) : undefined
                    }
                }
            );
        }

        return result;
    }

    /**
     * Validate that a value is not null or undefined
     * @param value Value to validate
     * @param name Value name
     * @param options Validation options
     * @returns Validation result
     */
    public validateRequired(
        value: any,
        name: string,
        options: ValidationOptions = {}
    ): ValidationResult {
        return this.validate(
            value,
            (val) => {
                const valid = val !== null && val !== undefined;

                return {
                    valid,
                    level: options.level || ValidationLevel.ERROR,
                    message: valid ? `${name} is valid` : `${name} is required`,
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                };
            },
            options
        );
    }

    /**
     * Validate that a value is of a specific type
     * @param value Value to validate
     * @param type Expected type
     * @param name Value name
     * @param options Validation options
     * @returns Validation result
     */
    public validateType(
        value: any,
        type: string,
        name: string,
        options: ValidationOptions = {}
    ): ValidationResult {
        return this.validate(
            value,
            (val) => {
                // Handle null and undefined
                if (val === null || val === undefined) {
                    return {
                        valid: false,
                        level: options.level || ValidationLevel.ERROR,
                        message: `${name} is required`,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    };
                }

                let valid = false;

                switch (type) {
                    case 'string':
                        valid = typeof val === 'string';
                        break;
                    case 'number':
                        valid = typeof val === 'number' && !isNaN(val);
                        break;
                    case 'boolean':
                        valid = typeof val === 'boolean';
                        break;
                    case 'object':
                        valid = typeof val === 'object' && val !== null;
                        break;
                    case 'array':
                        valid = Array.isArray(val);
                        break;
                    case 'function':
                        valid = typeof val === 'function';
                        break;
                    case 'buffer':
                        valid = Buffer.isBuffer(val);
                        break;
                    default:
                        valid = typeof val === type;
                        break;
                }

                return {
                    valid,
                    level: options.level || ValidationLevel.ERROR,
                    message: valid ? `${name} is valid` : `${name} must be a ${type}`,
                    code: ErrorCode.INVALID_TYPE,
                    details: {
                        expectedType: type,
                        actualType: Array.isArray(val) ? 'array' : typeof val
                    }
                };
            },
            options
        );
    }

    /**
     * Validate that a number is within a range
     * @param value Value to validate
     * @param min Minimum value (inclusive)
     * @param max Maximum value (inclusive)
     * @param name Value name
     * @param options Validation options
     * @returns Validation result
     */
    public validateRange(
        value: number,
        min: number,
        max: number,
        name: string,
        options: ValidationOptions = {}
    ): ValidationResult {
        return this.validate(
            value,
            (val) => {
                // Handle null and undefined
                if (val === null || val === undefined) {
                    return {
                        valid: false,
                        level: options.level || ValidationLevel.ERROR,
                        message: `${name} is required`,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    };
                }

                // Handle non-numbers
                if (typeof val !== 'number' || isNaN(val)) {
                    return {
                        valid: false,
                        level: options.level || ValidationLevel.ERROR,
                        message: `${name} must be a number`,
                        code: ErrorCode.INVALID_TYPE,
                        details: {
                            expectedType: 'number',
                            actualType: typeof val
                        }
                    };
                }

                const valid = val >= min && val <= max;

                return {
                    valid,
                    level: options.level || ValidationLevel.ERROR,
                    message: valid ? `${name} is valid` : `${name} must be between ${min} and ${max}`,
                    code: ErrorCode.INVALID_VALUE,
                    details: {
                        min,
                        max,
                        actual: val
                    }
                };
            },
            options
        );
    }

    /**
     * Validate that a string matches a regular expression
     * @param value Value to validate
     * @param regex Regular expression to match
     * @param name Value name
     * @param options Validation options
     * @returns Validation result
     */
    public validateFormat(
        value: string,
        regex: RegExp,
        name: string,
        options: ValidationOptions = {}
    ): ValidationResult {
        return this.validate(
            value,
            (val) => {
                // Handle null and undefined
                if (val === null || val === undefined) {
                    return {
                        valid: false,
                        level: options.level || ValidationLevel.ERROR,
                        message: `${name} is required`,
                        code: ErrorCode.MISSING_REQUIRED_FIELD
                    };
                }

                // Handle non-strings
                if (typeof val !== 'string') {
                    return {
                        valid: false,
                        level: options.level || ValidationLevel.ERROR,
                        message: `${name} must be a string`,
                        code: ErrorCode.INVALID_TYPE,
                        details: {
                            expectedType: 'string',
                            actualType: typeof val
                        }
                    };
                }

                const valid = regex.test(val);

                return {
                    valid,
                    level: options.level || ValidationLevel.ERROR,
                    message: valid ? `${name} is valid` : `${name} has an invalid format`,
                    code: ErrorCode.INVALID_FORMAT,
                    details: {
                        regex: regex.toString(),
                        actual: val
                    }
                };
            },
            options
        );
    }

    /**
     * Validate that a file exists
     * @param filePath File path to validate
     * @param options Validation options
     * @returns Validation result
     */
    public async validateFileExists(
        filePath: string,
        options: ValidationOptions = {}
    ): Promise<ValidationResult> {
        try {
            // Handle null and undefined
            if (filePath === null || filePath === undefined) {
                const result: ValidationResult = {
                    valid: false,
                    level: options.level || ValidationLevel.ERROR,
                    message: 'File path is required',
                    code: ErrorCode.MISSING_REQUIRED_FIELD
                };

                this.logValidationResult(result, options.context);

                if (options.throwOnFailure) {
                    throw errorHandler.createError(
                        result.code,
                        result.message,
                        {
                            category: ErrorCategory.VALIDATION,
                            severity: this.levelToSeverity(result.level),
                            context: {
                                component: 'Validator',
                                operation: 'validateFileExists',
                                ...options.context
                            }
                        }
                    );
                }

                return result;
            }

            // Handle non-strings
            if (typeof filePath !== 'string') {
                const result: ValidationResult = {
                    valid: false,
                    level: options.level || ValidationLevel.ERROR,
                    message: 'File path must be a string',
                    code: ErrorCode.INVALID_TYPE,
                    details: {
                        expectedType: 'string',
                        actualType: typeof filePath
                    }
                };

                this.logValidationResult(result, options.context);

                if (options.throwOnFailure) {
                    throw errorHandler.createError(
                        result.code,
                        result.message,
                        {
                            category: ErrorCategory.VALIDATION,
                            severity: this.levelToSeverity(result.level),
                            context: {
                                component: 'Validator',
                                operation: 'validateFileExists',
                                ...options.context
                            }
                        }
                    );
                }

                return result;
            }

            // Check if file exists
            await fsAccess(filePath, fs.constants.F_OK);

            // Get file stats
            const stats = await fsStat(filePath);

            // Check if it's a file
            const valid = stats.isFile();

            const result: ValidationResult = {
                valid,
                level: options.level || ValidationLevel.ERROR,
                message: valid ? `File ${filePath} exists` : `Path ${filePath} is not a file`,
                code: valid ? ErrorCode.NONE : ErrorCode.RESOURCE_NOT_FOUND,
                details: {
                    path: filePath,
                    isDirectory: stats.isDirectory(),
                    size: stats.size,
                    modified: stats.mtime
                }
            };

            this.logValidationResult(result, options.context);

            if (options.throwOnFailure && !valid) {
                throw errorHandler.createError(
                    result.code,
                    result.message,
                    {
                        category: ErrorCategory.VALIDATION,
                        severity: this.levelToSeverity(result.level),
                        context: {
                            component: 'Validator',
                            operation: 'validateFileExists',
                            parameters: { filePath },
                            ...options.context
                        }
                    }
                );
            }

            return result;
        } catch (error: any) {
            const isAccessError = error.code === 'ENOENT';

            const result: ValidationResult = {
                valid: false,
                level: options.level || ValidationLevel.ERROR,
                message: isAccessError ? `File ${filePath} does not exist` : `Error checking file ${filePath}: ${error.message}`,
                code: isAccessError ? ErrorCode.RESOURCE_NOT_FOUND : ErrorCode.SYSTEM_IO_ERROR,
                details: {
                    path: filePath,
                    error: error.message,
                    code: error.code
                }
            };

            this.logValidationResult(result, options.context);

            if (options.throwOnFailure) {
                throw errorHandler.createError(
                    result.code,
                    result.message,
                    {
                        category: isAccessError ? ErrorCategory.VALIDATION : ErrorCategory.SYSTEM,
                        severity: this.levelToSeverity(result.level),
                        context: {
                            component: 'Validator',
                            operation: 'validateFileExists',
                            parameters: { filePath },
                            ...options.context
                        },
                        cause: error
                    }
                );
            }

            return result;
        }
    }

    /**
     * Convert validation level to error severity
     * @param level Validation level
     * @returns Error severity
     * @private
     */
    private levelToSeverity(level: ValidationLevel): ErrorSeverity {
        switch (level) {
            case ValidationLevel.CRITICAL:
                return ErrorSeverity.FATAL;
            case ValidationLevel.ERROR:
                return ErrorSeverity.ERROR;
            case ValidationLevel.WARNING:
                return ErrorSeverity.WARNING;
            case ValidationLevel.INFO:
                return ErrorSeverity.INFO;
            default:
                return ErrorSeverity.ERROR;
        }
    }

    /**
     * Log validation result
     * @param result Validation result
     * @param context Validation context
     * @private
     */
    private logValidationResult(result: ValidationResult, context?: Record<string, any>): void {
        const contextStr = context ? ` (${JSON.stringify(context)})` : '';

        if (result.valid) {
            logger.debug(`Validation passed: ${result.message}${contextStr}`);
        } else {
            switch (result.level) {
                case ValidationLevel.CRITICAL:
                    logger.error(`CRITICAL validation failed: ${result.message}${contextStr}`);
                    break;
                case ValidationLevel.ERROR:
                    logger.error(`Validation failed: ${result.message}${contextStr}`);
                    break;
                case ValidationLevel.WARNING:
                    logger.warn(`Validation warning: ${result.message}${contextStr}`);
                    break;
                case ValidationLevel.INFO:
                    logger.info(`Validation info: ${result.message}${contextStr}`);
                    break;
            }
        }
    }
}
