/**
 * Enhanced Conflict Detector
 * Improved conflict detection with base game filtering and resource type context
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { GameInstallationService } from '../gameDetection/gameInstallationService.js';

export interface EnhancedConflict {
    id: string;
    type: string;
    group: string;
    instance: string;
    tgiKey: string;
    conflictingPackages: ConflictingPackage[];
    severity: 'critical' | 'warning' | 'info';
    resourceType: string;
    resourceCategory: string;
    description: string;
    isBaseGameResource: boolean;
}

export interface ConflictingPackage {
    packageId: number;
    packageName: string;
    packagePath: string;
    resourceId: number;
}

export interface ConflictDetectionOptions {
    filterBaseGame: boolean;
    minimumSeverity: 'critical' | 'warning' | 'info';
    resourceTypes?: number[];
    excludePackages?: number[];
}

// Resource type mappings for context
const RESOURCE_TYPE_INFO: { [type: number]: { name: string; category: string; severity: 'critical' | 'warning' | 'info' } } = {
    0x034AEECB: { name: 'CAS_PART', category: 'Create-a-Sim', severity: 'critical' },
    0x0354796A: { name: 'SKIN_TONE', category: 'Create-a-Sim', severity: 'critical' },
    0x545AC67A: { name: 'TRAIT', category: 'Gameplay', severity: 'critical' },
    0x9D1AB874: { name: 'SCRIPT', category: 'Python Scripts', severity: 'critical' },
    0xC5F6763E: { name: 'SCRIPT_MOD', category: 'Script Mods', severity: 'critical' },
    0x319E4F1D: { name: 'CATALOG_OBJECT', category: 'Build/Buy', severity: 'warning' },
    0xD5F0F921: { name: 'CATALOG_WALL', category: 'Build/Buy', severity: 'warning' },
    0xB4F762C9: { name: 'CATALOG_FLOOR', category: 'Build/Buy', severity: 'warning' },
    0x220557DA: { name: 'STRING_TABLE', category: 'Localization', severity: 'info' },
    0xB6D837C0: { name: 'DDS_IMAGE', category: 'Textures', severity: 'info' },
    0x00B2D882: { name: 'PNG_IMAGE', category: 'Textures', severity: 'info' },
    0x545AC67A: { name: 'SIMDATA', category: 'Data', severity: 'warning' },
    0xC0DB5AE7: { name: 'OBJECT_DEFINITION', category: 'Objects', severity: 'warning' },
    0x3C1AF1F2: { name: 'FOOTPRINT', category: 'Objects', severity: 'warning' },
    0xD3044521: { name: 'SLOT', category: 'Objects', severity: 'info' }
};

export class EnhancedConflictDetector {
    private logger: Logger;
    private databaseService: DatabaseService;
    private gameInstallationService: GameInstallationService;

    constructor(databaseService: DatabaseService, gameInstallationService: GameInstallationService) {
        this.logger = Logger.getInstance().child({ component: 'EnhancedConflictDetector' });
        this.databaseService = databaseService;
        this.gameInstallationService = gameInstallationService;
    }

    /**
     * Detect conflicts with enhanced filtering and context
     */
    public async detectConflicts(options: ConflictDetectionOptions = { filterBaseGame: true, minimumSeverity: 'info' }): Promise<EnhancedConflict[]> {
        this.logger.info('Starting enhanced conflict detection...');

        try {
            // Get raw conflicts from database
            const rawConflicts = await this.getRawConflicts(options);
            this.logger.info(`Found ${rawConflicts.length} potential conflicts`);

            // Filter and enhance conflicts
            const enhancedConflicts: EnhancedConflict[] = [];

            for (const conflict of rawConflicts) {
                const enhanced = await this.enhanceConflict(conflict, options);
                if (enhanced) {
                    enhancedConflicts.push(enhanced);
                }
            }

            // Sort by severity and resource type
            enhancedConflicts.sort((a, b) => {
                const severityOrder = { critical: 3, warning: 2, info: 1 };
                const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
                if (severityDiff !== 0) return severityDiff;
                
                return a.resourceType.localeCompare(b.resourceType);
            });

            this.logger.info(`Enhanced conflict detection completed. Found ${enhancedConflicts.length} meaningful conflicts`);
            return enhancedConflicts;
        } catch (error) {
            this.logger.error('Error during enhanced conflict detection:', error);
            throw error;
        }
    }

    /**
     * Get raw conflicts from database
     */
    private async getRawConflicts(options: ConflictDetectionOptions): Promise<any[]> {
        let query = `
            SELECT 
                r.type,
                r."group",
                r.instance,
                r.type || '-' || r."group" || '-' || r.instance as tgi_key,
                COUNT(*) as conflict_count,
                GROUP_CONCAT(r.packageId) as package_ids,
                GROUP_CONCAT(r.id) as resource_ids,
                GROUP_CONCAT(p.name) as package_names,
                GROUP_CONCAT(p.path) as package_paths
            FROM Resources r
            JOIN Packages p ON r.packageId = p.id
        `;

        const params: any[] = [];

        // Add WHERE conditions
        const conditions: string[] = [];

        if (options.resourceTypes && options.resourceTypes.length > 0) {
            conditions.push(`r.type IN (${options.resourceTypes.map(() => '?').join(', ')})`);
            params.push(...options.resourceTypes);
        }

        if (options.excludePackages && options.excludePackages.length > 0) {
            conditions.push(`r.packageId NOT IN (${options.excludePackages.map(() => '?').join(', ')})`);
            params.push(...options.excludePackages);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += `
            GROUP BY r.type, r."group", r.instance
            HAVING COUNT(*) > 1
            ORDER BY conflict_count DESC, r.type
        `;

        return await this.databaseService.executeQuery(query, params);
    }

    /**
     * Enhance a raw conflict with additional context and filtering
     */
    private async enhanceConflict(rawConflict: any, options: ConflictDetectionOptions): Promise<EnhancedConflict | null> {
        const tgiKey = rawConflict.tgi_key;
        const resourceType = parseInt(rawConflict.type);

        // Check if this is a base game resource (if filtering enabled)
        let isBaseGameResource = false;
        if (options.filterBaseGame) {
            isBaseGameResource = await this.gameInstallationService.isBaseGameResource(tgiKey);
            if (isBaseGameResource) {
                this.logger.debug(`Filtering out base game resource: ${tgiKey}`);
                return null; // Skip base game resources
            }
        }

        // Get resource type information
        const typeInfo = RESOURCE_TYPE_INFO[resourceType] || {
            name: `UNKNOWN_${resourceType.toString(16).toUpperCase()}`,
            category: 'Unknown',
            severity: 'info' as const
        };

        // Check minimum severity filter
        const severityOrder = { critical: 3, warning: 2, info: 1 };
        if (severityOrder[typeInfo.severity] < severityOrder[options.minimumSeverity]) {
            return null; // Skip conflicts below minimum severity
        }

        // Parse conflicting packages
        const packageIds = rawConflict.package_ids.split(',').map((id: string) => parseInt(id));
        const resourceIds = rawConflict.resource_ids.split(',').map((id: string) => parseInt(id));
        const packageNames = rawConflict.package_names.split(',');
        const packagePaths = rawConflict.package_paths.split(',');

        const conflictingPackages: ConflictingPackage[] = packageIds.map((packageId: number, index: number) => ({
            packageId,
            packageName: packageNames[index],
            packagePath: packagePaths[index],
            resourceId: resourceIds[index]
        }));

        // Generate description
        const description = this.generateConflictDescription(typeInfo, conflictingPackages);

        return {
            id: tgiKey,
            type: rawConflict.type.toString(),
            group: rawConflict.group.toString(),
            instance: rawConflict.instance,
            tgiKey,
            conflictingPackages,
            severity: typeInfo.severity,
            resourceType: typeInfo.name,
            resourceCategory: typeInfo.category,
            description,
            isBaseGameResource
        };
    }

    /**
     * Generate a human-readable description for the conflict
     */
    private generateConflictDescription(typeInfo: any, conflictingPackages: ConflictingPackage[]): string {
        const packageCount = conflictingPackages.length;
        const packageNames = conflictingPackages.map(p => p.packageName).join(', ');

        switch (typeInfo.category) {
            case 'Create-a-Sim':
                return `${packageCount} mods modify the same CAS part (${typeInfo.name}). This may cause visual conflicts or missing content in Create-a-Sim. Conflicting mods: ${packageNames}`;
            
            case 'Gameplay':
                return `${packageCount} mods modify the same gameplay element (${typeInfo.name}). This may cause unexpected behavior or crashes. Conflicting mods: ${packageNames}`;
            
            case 'Python Scripts':
                return `${packageCount} mods contain conflicting Python scripts (${typeInfo.name}). This may cause script errors or mod functionality issues. Conflicting mods: ${packageNames}`;
            
            case 'Build/Buy':
                return `${packageCount} mods modify the same Build/Buy catalog item (${typeInfo.name}). This may cause duplicate or missing items in the catalog. Conflicting mods: ${packageNames}`;
            
            case 'Textures':
                return `${packageCount} mods modify the same texture (${typeInfo.name}). Only one texture will be used in-game. Conflicting mods: ${packageNames}`;
            
            case 'Objects':
                return `${packageCount} mods modify the same object definition (${typeInfo.name}). This may cause object behavior issues or visual glitches. Conflicting mods: ${packageNames}`;
            
            case 'Localization':
                return `${packageCount} mods modify the same text strings (${typeInfo.name}). This may cause text display issues or missing translations. Conflicting mods: ${packageNames}`;
            
            default:
                return `${packageCount} mods modify the same resource (${typeInfo.name}). This may cause conflicts depending on the resource type. Conflicting mods: ${packageNames}`;
        }
    }

    /**
     * Get conflict statistics
     */
    public async getConflictStatistics(options: ConflictDetectionOptions = { filterBaseGame: true, minimumSeverity: 'info' }): Promise<{
        totalConflicts: number;
        conflictsBySeverity: { [severity: string]: number };
        conflictsByCategory: { [category: string]: number };
        mostConflictedResourceTypes: { type: string; count: number }[];
    }> {
        try {
            const conflicts = await this.detectConflicts(options);

            const stats = {
                totalConflicts: conflicts.length,
                conflictsBySeverity: { critical: 0, warning: 0, info: 0 },
                conflictsByCategory: {} as { [category: string]: number },
                mostConflictedResourceTypes: [] as { type: string; count: number }[]
            };

            const typeCount: { [type: string]: number } = {};

            for (const conflict of conflicts) {
                // Count by severity
                stats.conflictsBySeverity[conflict.severity]++;

                // Count by category
                if (!stats.conflictsByCategory[conflict.resourceCategory]) {
                    stats.conflictsByCategory[conflict.resourceCategory] = 0;
                }
                stats.conflictsByCategory[conflict.resourceCategory]++;

                // Count by resource type
                if (!typeCount[conflict.resourceType]) {
                    typeCount[conflict.resourceType] = 0;
                }
                typeCount[conflict.resourceType]++;
            }

            // Sort resource types by conflict count
            stats.mostConflictedResourceTypes = Object.entries(typeCount)
                .map(([type, count]) => ({ type, count }))
                .sort((a, b) => b.count - a.count)
                .slice(0, 10);

            return stats;
        } catch (error) {
            this.logger.error('Error getting conflict statistics:', error);
            return {
                totalConflicts: 0,
                conflictsBySeverity: { critical: 0, warning: 0, info: 0 },
                conflictsByCategory: {},
                mostConflictedResourceTypes: []
            };
        }
    }

    /**
     * Check if a specific TGI is conflicted
     */
    public async isResourceConflicted(type: number, group: number, instance: string, options: ConflictDetectionOptions = { filterBaseGame: true, minimumSeverity: 'info' }): Promise<boolean> {
        const tgiKey = `${type}-${group}-${instance}`;
        
        // Check if it's a base game resource first
        if (options.filterBaseGame) {
            const isBaseGame = await this.gameInstallationService.isBaseGameResource(tgiKey);
            if (isBaseGame) {
                return false; // Base game resources are not considered conflicts
            }
        }

        // Check for conflicts
        const result = await this.databaseService.executeQuery(`
            SELECT COUNT(*) as count
            FROM Resources
            WHERE type = ? AND "group" = ? AND instance = ?
        `, [type, group, instance]);

        return result[0].count > 1;
    }
}
