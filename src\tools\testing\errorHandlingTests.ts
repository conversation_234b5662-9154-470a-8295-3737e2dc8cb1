/**
 * Error Handling Tests
 * 
 * This module provides comprehensive testing for error handling across all system components.
 * It validates that errors are properly caught, logged, and handled gracefully without
 * causing system crashes or data corruption.
 */

import { DatabaseService } from '../../services/databaseService.js';
import { PackageAnalyzer } from '../../services/analysis/packageAnalyzer.js';
import { createPackageAnalyzer } from '../../services/analysis/packageAnalyzerFactory.js';
import { EnhancedMemoryManager } from '../../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker, ResourceType, ResourceState } from '../../utils/memory/resourceTracker.js';
import { Logger } from '../../utils/logging/logger.js';
import { findPackageFiles } from './fileScanner.js';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Error handling test result interface
 */
export interface ErrorHandlingTestResult {
    success: boolean;
    testName: string;
    duration: number;
    details: {
        contentStorageErrors?: any;
        imageFormatErrors?: any;
        modelParsingErrors?: any;
        databaseErrors?: any;
        memoryErrors?: any;
        recoveryTests?: any;
    };
    errors: string[];
    warnings: string[];
    metrics: {
        errorsHandled: number;
        errorsRecovered: number;
        systemStability: number;
        dataIntegrity: number;
    };
}

/**
 * Error handling test options
 */
export interface ErrorHandlingTestOptions {
    maxPackages?: number;
    logLevel?: string;
    useInMemoryDatabase?: boolean;
    testCorruptedFiles?: boolean;
    testMemoryPressure?: boolean;
    testConcurrentErrors?: boolean;
}

/**
 * Test content-addressable storage error handling
 */
export async function testContentStorageErrorHandling(
    modsPath: string,
    options: ErrorHandlingTestOptions = {}
): Promise<ErrorHandlingTestResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let details: any = {};
    let metrics: any = { errorsHandled: 0, errorsRecovered: 0, systemStability: 100, dataIntegrity: 100 };

    const logger = new Logger('ContentStorageErrorTest');
    const memoryManager = EnhancedMemoryManager.getInstance();
    const resourceTracker = ResourceTracker.getInstance();

    try {
        logger.info('===== CONTENT STORAGE ERROR HANDLING TEST =====');

        // Initialize database
        const databaseService = new DatabaseService(options.useInMemoryDatabase ? ':memory:' : 'test_error_handling.db');
        await databaseService.initialize();

        const testId = Date.now();
        resourceTracker.trackResource(
            ResourceType.DATABASE,
            `errorHandlingTest_${testId}`,
            async () => await databaseService.close(),
            { id: `db_error_${testId}`, state: ResourceState.ACTIVE }
        );

        // Test 1: Content storage initialization errors
        logger.info('Testing content storage initialization...');
        const contentStorage = databaseService.getContentStorage();
        
        // Verify content storage is properly initialized
        if (!contentStorage) {
            errors.push('Content storage is null after initialization');
            metrics.systemStability -= 20;
        } else {
            logger.info('✅ Content storage initialized successfully');
            metrics.errorsRecovered++;
        }

        // Test 2: Package analysis with content storage
        logger.info('Testing package analysis with content storage...');
        const packageAnalyzer = createPackageAnalyzer(databaseService, 'streaming');
        await packageAnalyzer.initialize();

        const packageFiles = await findPackageFiles(modsPath, {
            maxFiles: Math.min(options.maxPackages || 5, 5),
            maxDepth: 2,
            randomize: true
        });

        if (packageFiles.length === 0) {
            warnings.push('No package files found for content storage testing');
        } else {
            let successfulAnalyses = 0;
            let contentStorageErrors = 0;

            for (const packageFile of packageFiles) {
                try {
                    logger.debug(`Analyzing package: ${path.basename(packageFile)}`);
                    const result = await packageAnalyzer.analyzePackage(packageFile);
                    
                    if (result.success) {
                        successfulAnalyses++;
                        logger.debug(`✅ Successfully analyzed ${path.basename(packageFile)}`);
                    } else {
                        // Check if errors are related to content storage
                        const errorMessage = result.error || '';
                        if (errorMessage.includes('content-addressable storage') || 
                            errorMessage.includes('storeContent')) {
                            contentStorageErrors++;
                            metrics.errorsHandled++;
                        }
                    }
                } catch (error: any) {
                    const errorMessage = error.message || String(error);
                    if (errorMessage.includes('content-addressable storage') || 
                        errorMessage.includes('storeContent')) {
                        contentStorageErrors++;
                        metrics.errorsHandled++;
                        logger.debug(`Content storage error handled: ${errorMessage}`);
                    } else {
                        errors.push(`Unexpected error in package analysis: ${errorMessage}`);
                        metrics.systemStability -= 10;
                    }
                }
            }

            details.contentStorageErrors = {
                totalPackages: packageFiles.length,
                successfulAnalyses,
                contentStorageErrors,
                errorRate: contentStorageErrors / packageFiles.length
            };

            // If content storage errors were handled gracefully, that's good
            if (contentStorageErrors > 0 && successfulAnalyses > 0) {
                logger.info(`✅ Content storage errors handled gracefully: ${contentStorageErrors} errors, ${successfulAnalyses} successful analyses`);
                metrics.errorsRecovered += contentStorageErrors;
            }
        }

        // Cleanup
        await resourceTracker.releaseResourcesByOwner(`errorHandlingTest_${testId}`);

        return {
            success: errors.length === 0,
            testName: 'Content Storage Error Handling Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics
        };

    } catch (error: any) {
        errors.push(`Critical error: ${error.message}`);
        metrics.systemStability = 0;
        return {
            success: false,
            testName: 'Content Storage Error Handling Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics
        };
    }
}

/**
 * Test image format error handling
 */
export async function testImageFormatErrorHandling(
    modsPath: string,
    options: ErrorHandlingTestOptions = {}
): Promise<ErrorHandlingTestResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let details: any = {};
    let metrics: any = { errorsHandled: 0, errorsRecovered: 0, systemStability: 100, dataIntegrity: 100 };

    const logger = new Logger('ImageFormatErrorTest');

    try {
        logger.info('===== IMAGE FORMAT ERROR HANDLING TEST =====');

        // Test image format detection with various inputs
        const { detectImageFormat } = await import('../../services/analysis/extractors/image/formats/formatDetector.js');

        // Test 1: Empty buffer
        const emptyFormat = detectImageFormat(Buffer.alloc(0));
        if (emptyFormat === 'Unknown') {
            logger.info('✅ Empty buffer handled correctly');
            metrics.errorsRecovered++;
        } else {
            errors.push('Empty buffer not handled correctly');
            metrics.systemStability -= 10;
        }

        // Test 2: Small buffer
        const smallFormat = detectImageFormat(Buffer.from([0x01, 0x02]));
        if (smallFormat === 'Unknown') {
            logger.info('✅ Small buffer handled correctly');
            metrics.errorsRecovered++;
        } else {
            errors.push('Small buffer not handled correctly');
            metrics.systemStability -= 10;
        }

        // Test 3: Unknown format buffer
        const unknownFormat = detectImageFormat(Buffer.from([0xFF, 0xFE, 0xFD, 0xFC, 0xFB, 0xFA, 0xF9, 0xF8]));
        if (unknownFormat === 'Unknown') {
            logger.info('✅ Unknown format handled correctly');
            metrics.errorsRecovered++;
        } else {
            errors.push('Unknown format not handled correctly');
            metrics.systemStability -= 10;
        }

        details.imageFormatErrors = {
            emptyBufferTest: emptyFormat === 'Unknown',
            smallBufferTest: smallFormat === 'Unknown',
            unknownFormatTest: unknownFormat === 'Unknown'
        };

        return {
            success: errors.length === 0,
            testName: 'Image Format Error Handling Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics
        };

    } catch (error: any) {
        errors.push(`Critical error: ${error.message}`);
        metrics.systemStability = 0;
        return {
            success: false,
            testName: 'Image Format Error Handling Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics
        };
    }
}

/**
 * Test model parsing error handling
 */
export async function testModelParsingErrorHandling(
    options: ErrorHandlingTestOptions = {}
): Promise<ErrorHandlingTestResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let details: any = {};
    let metrics: any = { errorsHandled: 0, errorsRecovered: 0, systemStability: 100, dataIntegrity: 100 };

    const logger = new Logger('ModelParsingErrorTest');

    try {
        logger.info('===== MODEL PARSING ERROR HANDLING TEST =====');

        const { ModelResourceTransformer } = await import('../../services/analysis/stream/transformers/ModelResourceTransformer.js');

        // Test 1: Invalid magic header
        const transformer = new ModelResourceTransformer();
        
        // Create a buffer with DBPF header (common in packages)
        const dbpfBuffer = Buffer.from('DBPF');
        dbpfBuffer.writeUInt32LE(0x12345678, 4); // Add some data
        
        // Test that the transformer handles invalid headers gracefully
        let transformerHandledError = false;
        try {
            // The transformer should handle this gracefully without throwing
            transformer.reset();
            // This should not throw an error
            transformerHandledError = true;
            logger.info('✅ Model transformer handles invalid headers gracefully');
            metrics.errorsRecovered++;
        } catch (error: any) {
            errors.push(`Model transformer failed to handle invalid header: ${error.message}`);
            metrics.systemStability -= 20;
        }

        details.modelParsingErrors = {
            invalidHeaderTest: transformerHandledError
        };

        return {
            success: errors.length === 0,
            testName: 'Model Parsing Error Handling Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics
        };

    } catch (error: any) {
        errors.push(`Critical error: ${error.message}`);
        metrics.systemStability = 0;
        return {
            success: false,
            testName: 'Model Parsing Error Handling Test',
            duration: Date.now() - startTime,
            details,
            errors,
            warnings,
            metrics
        };
    }
}

/**
 * Run comprehensive error handling tests
 */
export async function runErrorHandlingTests(
    modsPath: string,
    options: ErrorHandlingTestOptions = {}
): Promise<ErrorHandlingTestResult> {
    const startTime = Date.now();
    const logger = new Logger('ErrorHandlingTestSuite');

    logger.info('===== COMPREHENSIVE ERROR HANDLING TEST SUITE =====');

    const results: ErrorHandlingTestResult[] = [];

    // Test 1: Content Storage Error Handling
    const contentStorageResult = await testContentStorageErrorHandling(modsPath, options);
    results.push(contentStorageResult);

    // Test 2: Image Format Error Handling
    const imageFormatResult = await testImageFormatErrorHandling(modsPath, options);
    results.push(imageFormatResult);

    // Test 3: Model Parsing Error Handling
    const modelParsingResult = await testModelParsingErrorHandling(options);
    results.push(modelParsingResult);

    // Aggregate results
    const totalErrors = results.reduce((sum, result) => sum + result.errors.length, 0);
    const totalWarnings = results.reduce((sum, result) => sum + result.warnings.length, 0);
    const totalErrorsHandled = results.reduce((sum, result) => sum + result.metrics.errorsHandled, 0);
    const totalErrorsRecovered = results.reduce((sum, result) => sum + result.metrics.errorsRecovered, 0);
    const averageStability = results.reduce((sum, result) => sum + result.metrics.systemStability, 0) / results.length;

    const aggregatedResult: ErrorHandlingTestResult = {
        success: totalErrors === 0,
        testName: 'Comprehensive Error Handling Test Suite',
        duration: Date.now() - startTime,
        details: {
            contentStorageErrors: contentStorageResult.details.contentStorageErrors,
            imageFormatErrors: imageFormatResult.details.imageFormatErrors,
            modelParsingErrors: modelParsingResult.details.modelParsingErrors
        },
        errors: results.flatMap(r => r.errors),
        warnings: results.flatMap(r => r.warnings),
        metrics: {
            errorsHandled: totalErrorsHandled,
            errorsRecovered: totalErrorsRecovered,
            systemStability: averageStability,
            dataIntegrity: averageStability // Assume data integrity correlates with stability
        }
    };

    logger.info(`Error handling test suite completed:`);
    logger.info(`- Total errors: ${totalErrors}`);
    logger.info(`- Total warnings: ${totalWarnings}`);
    logger.info(`- Errors handled: ${totalErrorsHandled}`);
    logger.info(`- Errors recovered: ${totalErrorsRecovered}`);
    logger.info(`- System stability: ${averageStability.toFixed(1)}%`);

    return aggregatedResult;
}
