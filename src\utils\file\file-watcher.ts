﻿﻿﻿﻿﻿﻿import { watch, FSWatcher } from 'chokidar';
import { existsSync, mkdirSync, copyFileSync, renameSync, statSync } from 'fs';
import { promises as fsPromises } from 'fs';
import { join, dirname, basename, extname } from 'path';
import { organizePackages, getSuggestedFolderName } from './mod-organization.js';
import { ModCategory } from '../../types/resource/Package.js';
import { ConflictResult, ConflictSeverity, ConflictType } from '../../types/conflict/ConflictTypes.js';
import { ConflictInfo } from '../../types/conflict/index.js';
import { EventEmitter } from 'events';
import { metadataCache } from '../../lib/package/metadataCache.js';
import { PackageAnalysisService } from '../../services/analysis/packageAnalysisService.js';
import { AnalysisTier } from '../../types/resource/resourceAnalysis.js';
import { BinaryResourceType } from '../../types/resource/core.js';
import { <PERSON>Key } from '../../types/resource/interfaces.js';
import { Logger } from '../logging/logger.js';
import { PackageMetadata, PackageAnalysisOptions } from '../../types/resource/Package.js';
import { AnalysisStatus } from '../../constants/analysis.js';

// Create a logger instance
const logger = new Logger('FileWatcher');

// Define an extended interface for conflict detection options
interface ExtendedPackageAnalysisOptions {
  compareWithPackage?: string;
  checkConflicts?: boolean;
  analysisDepth?: AnalysisTier;
}

// Fix the ConflictResult interface to match our usage
interface ExtendedConflictResult extends ConflictResult {
  files?: string[];
  affectedResources?: ResourceKey[];
}

// Create a proper package validation function
const validatePackage = async (
  filePath: string
): Promise<{ isValid: boolean; errors: string[] }> => {
  try {
    // Check if file exists and has a valid extension
    if (!existsSync(filePath)) {
      return { isValid: false, errors: ['File does not exist'] };
    }

    // Check file extension
    const ext = extname(filePath).toLowerCase();
    if (ext !== '.package' && ext !== '.ts4script') {
      return {
        isValid: false,
        errors: [`Invalid file extension: ${ext}. Expected .package or .ts4script`],
      };
    }

    // Check file size (>0 bytes)
    const stats = statSync(filePath);
    if (stats.size === 0) {
      return { isValid: false, errors: ['File is empty'] };
    }

    // Basic validation is enough for now
    return { isValid: true, errors: [] };
  } catch (error) {
    return {
      isValid: false,
      errors: [
        `Error validating package: ${error instanceof Error ? error.message : String(error)}`,
      ],
    };
  }
};

/**
 * Detect conflicts between packages
 * @param filePaths Paths to the packages to check for conflicts
 * @returns Array of ConflictInfo objects representing detected conflicts
 */
// Changed return type to Promise<ConflictInfo[]>
const detectConflicts = async (filePaths: string[] | string): Promise<ConflictInfo[]> => {
  console.log(
    `Detecting conflicts for: ${Array.isArray(filePaths) ? filePaths.join(', ') : filePaths}`
  );

  try {
    // Convert file paths to array
    const paths = Array.isArray(filePaths) ? filePaths : [filePaths];
    if (paths.length === 0) {
      return [];
    }

    // Initialize package analysis service
    const packageService = PackageAnalysisService.getInstance(logger);
    await packageService.initialize();

    console.log(`Analyzing ${paths.length} files for conflicts`);

    // Filter out invalid packages
    const validPaths: string[] = [];
    for (const path of paths) {
      const validation = await validatePackage(path);
      if (validation.isValid) {
        validPaths.push(path);
      } else {
        console.warn(
          `Skipping invalid package for conflict detection: ${path} - ${validation.errors.join('; ')}`
        );
      }
    }

    if (validPaths.length === 0) {
      console.warn('No valid packages to analyze for conflicts');
      return [];
    }

    // Analyze packages for conflicts
    const analysisResults = await packageService.analyzeMultiplePackages(validPaths); // Returns PackageAnalysisResult[]

    // Aggregate ConflictInfo objects from all package results
    const allConflicts: ConflictInfo[] = [];

    for (const packageResult of analysisResults) {
      // packageResult.conflicts should be ConflictInfo[]
      if (packageResult.conflicts && packageResult.conflicts.length > 0) {
        // Directly add the ConflictInfo objects to the aggregate list
        allConflicts.push(...packageResult.conflicts);
      }
    }

    // TODO: Consider if analyzeMultiplePackages needs refinement to better handle
    // returning cross-package conflicts directly vs. relying on service state.
    // For now, assume packageResult.conflicts contains the relevant conflicts.

    return allConflicts; // Return the flat list of ConflictInfo
  } catch (error) {
    console.error('Error detecting conflicts:', error);
    return [];
  }
};

/**
 * Get all package files in the mods folder
 * @param modsFolder Optional specific folder to scan, defaults to the system mods folder
 * @returns Promise resolving to an array of file paths
 */
const getAllPackageFiles = async (modsFolder?: string): Promise<string[]> => {
  try {
    // Get the service instance to access the mods directory
    const packageService = PackageAnalysisService.getInstance(logger);

    // Default to the mods directory from the service if none provided
    const folderToScan = modsFolder || (await packageService.getModsDirectory());

    if (!folderToScan || !existsSync(folderToScan)) {
      console.error(`Mods folder does not exist: ${folderToScan}`);
      return [];
    }

    // Array to store all found package files
    const packageFiles: string[] = [];

    // Recursively scan for package files
    const scanDirectory = async (directory: string) => {
      try {
        const entries = await fsPromises.readdir(directory, { withFileTypes: true });

        // Process entries in parallel for better performance
        const promises = entries.map(async entry => {
          const fullPath = join(directory, entry.name);

          if (entry.isDirectory()) {
            // Skip system directories and hidden directories
            if (
              entry.name.startsWith('.') ||
              entry.name === 'Backups' ||
              entry.name === 'Quarantine'
            ) {
              return;
            }

            // Recursively scan subdirectories
            await scanDirectory(fullPath);
          } else if (entry.isFile()) {
            // Check if this is a package file
            if (
              fullPath.toLowerCase().endsWith('.package') ||
              fullPath.toLowerCase().endsWith('.ts4script')
            ) {
              packageFiles.push(fullPath);
            }
          }
        });

        await Promise.all(promises);
      } catch (error) {
        console.error(`Error scanning directory ${directory}:`, error);
      }
    };

    // Start the recursive scan
    await scanDirectory(folderToScan);

    console.log(`Found ${packageFiles.length} package files in ${folderToScan}`);
    return packageFiles;
  } catch (error) {
    console.error('Error getting package files:', error);
    return [];
  }
};

/**
 * Events emitted by the file watcher
 */
export enum FileWatcherEvent {
  SCAN_STARTED = 'scan_started',
  SCAN_COMPLETED = 'scan_completed',
  FILE_ADDED = 'file_added',
  FILE_CHANGED = 'file_changed',
  FILE_REMOVED = 'file_removed',
  FILE_INVALID = 'file_invalid',
  FILE_CONFLICT = 'file_conflict',
  CONFLICT_DETECTED = 'conflict_detected',
  FILE_QUARANTINED = 'file_quarantined',
  FILE_ORGANIZED = 'file_organized',
  EXTERNAL_MODIFICATION = 'external_modification',
  ERROR = 'error',
}

/**
 * Event emitter for file watcher events
 */
export const fileWatcherEvents = new EventEmitter();

export interface FileWatcherOptions {
  watchDir: string;
  ignorePatterns?: string[];
  debounceTime?: number;
  maxFileSize?: number;
}

export class FileWatcher extends EventEmitter {
  private watcher: any;
  private options: FileWatcherOptions;
  private analysisService: PackageAnalysisService;
  private status: AnalysisStatus = AnalysisStatus.IDLE;
  private debounceTimer: NodeJS.Timeout | null = null;

  constructor(options: FileWatcherOptions, analysisService: PackageAnalysisService) {
    super();
    this.options = {
      debounceTime: 1000,
      maxFileSize: 100 * 1024 * 1024, // 100MB
      ...options
    };
    this.analysisService = analysisService;
    this.initializeWatcher();
  }

  private initializeWatcher(): void {
    this.watcher = watch(this.options.watchDir, {
      ignored: this.options.ignorePatterns || [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**'
      ],
      persistent: true,
      ignoreInitial: true
    });

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    this.watcher
      .on('add', (path: string) => this.handleFileChange('add', path))
      .on('change', (path: string) => this.handleFileChange('change', path))
      .on('unlink', (path: string) => this.handleFileChange('remove', path))
      .on('error', (error: Error) => this.emit('error', error));
  }

  private handleFileChange(event: 'add' | 'change' | 'remove', path: string): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      this.processFileChange(event, path);
    }, this.options.debounceTime);
  }

  private async processFileChange(event: 'add' | 'change' | 'remove', path: string): Promise<void> {
    try {
      this.status = AnalysisStatus.ANALYZING;
      this.emit('statusChange', this.status);

      switch (event) {
        case 'add':
        case 'change':
          await this.analyzeFile(path);
          break;
        case 'remove':
          this.emit('fileRemoved', path);
          break;
      }

      this.status = AnalysisStatus.COMPLETED;
    } catch (error) {
      this.status = AnalysisStatus.ERROR;
      this.emit('error', error);
    } finally {
      this.emit('statusChange', this.status);
    }
  }

  private async analyzeFile(path: string): Promise<void> {
    try {
      const result = await this.analysisService.analyzePackage(path);
      this.emit('analysisComplete', result);
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  public start(): void {
    if (this.status === AnalysisStatus.IDLE) {
      this.watcher.add(this.options.watchDir);
      this.status = AnalysisStatus.ANALYZING;
      this.emit('statusChange', this.status);
    }
  }

  public stop(): void {
    if (this.status !== AnalysisStatus.IDLE) {
      this.watcher.close();
      this.status = AnalysisStatus.IDLE;
      this.emit('statusChange', this.status);
    }
  }

  public getStatus(): AnalysisStatus {
    return this.status;
  }

  public dispose(): void {
    this.stop();
    this.removeAllListeners();
  }
}
