/**
 * Base Game Resource Extractor
 * Extracts resource information from base game files to filter them from conflict detection
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import { GameInstallationService, GameInstallation, GameFile, GameResource } from './gameInstallationService.js';
import { Package } from '@s4tk/models';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

export class BaseGameResourceExtractor {
    private logger: Logger;
    private databaseService: DatabaseService;
    private gameInstallationService: GameInstallationService;

    constructor(databaseService: DatabaseService, gameInstallationService: GameInstallationService) {
        this.logger = Logger.getInstance().child({ component: 'BaseGameResourceExtractor' });
        this.databaseService = databaseService;
        this.gameInstallationService = gameInstallationService;
    }

    /**
     * Extract base game resources from all detected installations
     */
    public async extractAllBaseGameResources(): Promise<void> {
        this.logger.info('Starting base game resource extraction...');

        const installations = await this.gameInstallationService.getGameInstallations();
        
        if (installations.length === 0) {
            this.logger.warn('No game installations found. Run game detection first.');
            return;
        }

        for (const installation of installations) {
            await this.extractResourcesFromInstallation(installation);
        }

        this.logger.info('Base game resource extraction completed');
    }

    /**
     * Extract resources from a specific game installation
     */
    private async extractResourcesFromInstallation(installation: GameInstallation): Promise<void> {
        this.logger.info(`Extracting resources from installation: ${installation.path}`);

        try {
            // Find all package files in the installation
            const packageFiles = await this.findPackageFiles(installation.path);
            this.logger.info(`Found ${packageFiles.length} package files in installation`);

            let processedCount = 0;
            const totalFiles = packageFiles.length;

            for (const packageFile of packageFiles) {
                try {
                    await this.extractResourcesFromPackage(installation, packageFile);
                    processedCount++;
                    
                    if (processedCount % 10 === 0) {
                        this.logger.info(`Processed ${processedCount}/${totalFiles} package files`);
                    }
                } catch (error) {
                    this.logger.warn(`Error processing package ${packageFile}:`, error);
                }
            }

            this.logger.info(`Completed extraction from installation: ${installation.path}`);
        } catch (error) {
            this.logger.error(`Error extracting resources from installation ${installation.path}:`, error);
        }
    }

    /**
     * Find all package files in the game installation
     */
    private async findPackageFiles(installationPath: string): Promise<string[]> {
        const packageFiles: string[] = [];
        
        // Common directories containing package files
        const searchDirs = [
            path.join(installationPath, 'Data', 'Client'),
            path.join(installationPath, 'Data', 'Shared'),
            path.join(installationPath, 'EP01'), // Get to Work
            path.join(installationPath, 'EP02'), // Get Together
            path.join(installationPath, 'EP03'), // City Living
            path.join(installationPath, 'EP04'), // Cats & Dogs
            path.join(installationPath, 'EP05'), // Seasons
            path.join(installationPath, 'EP06'), // Get Famous
            path.join(installationPath, 'EP07'), // Island Living
            path.join(installationPath, 'EP08'), // Discover University
            path.join(installationPath, 'EP09'), // Eco Lifestyle
            path.join(installationPath, 'EP10'), // Snowy Escape
            path.join(installationPath, 'EP11'), // Cottage Living
            path.join(installationPath, 'EP12'), // High School Years
            path.join(installationPath, 'EP13'), // Growing Together
            path.join(installationPath, 'EP14'), // Horse Ranch
            path.join(installationPath, 'EP15'), // For Rent
            path.join(installationPath, 'GP01'), // Outdoor Retreat
            path.join(installationPath, 'GP02'), // Spa Day
            path.join(installationPath, 'GP03'), // Dine Out
            path.join(installationPath, 'GP04'), // Vampires
            path.join(installationPath, 'GP05'), // Parenthood
            path.join(installationPath, 'GP06'), // Jungle Adventure
            path.join(installationPath, 'GP07'), // StrangerVille
            path.join(installationPath, 'GP08'), // Realm of Magic
            path.join(installationPath, 'GP09'), // Star Wars: Journey to Batuu
            path.join(installationPath, 'GP10'), // Dream Home Decorator
            path.join(installationPath, 'GP11'), // My Wedding Stories
            path.join(installationPath, 'GP12'), // Werewolves
            // Add more as needed
        ];

        for (const dir of searchDirs) {
            if (fs.existsSync(dir)) {
                const files = await this.findPackageFilesInDirectory(dir);
                packageFiles.push(...files);
            }
        }

        return packageFiles;
    }

    /**
     * Recursively find package files in a directory
     */
    private async findPackageFilesInDirectory(directory: string): Promise<string[]> {
        const packageFiles: string[] = [];

        try {
            const entries = fs.readdirSync(directory, { withFileTypes: true });

            for (const entry of entries) {
                const fullPath = path.join(directory, entry.name);

                if (entry.isDirectory()) {
                    // Recursively search subdirectories
                    const subFiles = await this.findPackageFilesInDirectory(fullPath);
                    packageFiles.push(...subFiles);
                } else if (entry.isFile() && entry.name.toLowerCase().endsWith('.package')) {
                    // Skip string packages as they don't contain conflictable resources
                    if (!entry.name.toLowerCase().includes('strings')) {
                        packageFiles.push(fullPath);
                    }
                }
            }
        } catch (error) {
            this.logger.debug(`Error reading directory ${directory}:`, error);
        }

        return packageFiles;
    }

    /**
     * Extract resources from a single package file
     */
    private async extractResourcesFromPackage(installation: GameInstallation, packagePath: string): Promise<void> {
        try {
            // Calculate file hash for tracking
            const fileHash = await this.calculateFileHash(packagePath);
            const fileName = path.basename(packagePath);
            const packType = this.determinePackType(packagePath);

            // Save game file record
            const gameFileId = await this.saveGameFile({
                installationId: installation.id!,
                filePath: packagePath,
                fileHash,
                packType,
                fileName
            });

            // Check if we've already processed this file
            const existingResources = await this.databaseService.executeQuery(
                'SELECT COUNT(*) as count FROM game_resources WHERE game_file_id = ?',
                [gameFileId]
            );

            if (existingResources[0].count > 0) {
                this.logger.debug(`Skipping already processed file: ${fileName}`);
                return;
            }

            // Read and parse the package file
            const buffer = fs.readFileSync(packagePath);
            const pkg = Package.from(buffer);

            // Extract resource information
            const resources: GameResource[] = [];
            
            for (const entry of pkg.entries) {
                const tgiKey = `${entry.type.toString(16).padStart(8, '0')}-${entry.group.toString(16).padStart(8, '0')}-${entry.instance.toString(16).padStart(16, '0')}`;
                
                resources.push({
                    gameFileId,
                    resourceType: entry.type,
                    resourceGroup: entry.group,
                    resourceInstance: entry.instance.toString(16).padStart(16, '0'),
                    tgiKey
                });
            }

            // Save resources in batches for performance
            await this.saveGameResourcesBatch(resources);
            
            this.logger.debug(`Extracted ${resources.length} resources from ${fileName}`);
        } catch (error) {
            this.logger.warn(`Error extracting resources from ${packagePath}:`, error);
        }
    }

    /**
     * Calculate SHA-256 hash of a file
     */
    private async calculateFileHash(filePath: string): Promise<string> {
        return new Promise((resolve, reject) => {
            const hash = crypto.createHash('sha256');
            const stream = fs.createReadStream(filePath);

            stream.on('data', (data) => hash.update(data));
            stream.on('end', () => resolve(hash.digest('hex')));
            stream.on('error', reject);
        });
    }

    /**
     * Determine pack type based on file path
     */
    private determinePackType(packagePath: string): 'base' | 'ep' | 'gp' | 'sp' {
        const lowerPath = packagePath.toLowerCase();
        
        if (lowerPath.includes('\\ep') || lowerPath.includes('/ep')) {
            return 'ep'; // Expansion Pack
        } else if (lowerPath.includes('\\gp') || lowerPath.includes('/gp')) {
            return 'gp'; // Game Pack
        } else if (lowerPath.includes('\\sp') || lowerPath.includes('/sp')) {
            return 'sp'; // Stuff Pack
        } else {
            return 'base'; // Base game
        }
    }

    /**
     * Save game file record to database
     */
    private async saveGameFile(gameFile: Omit<GameFile, 'id'>): Promise<number> {
        try {
            // Check if file already exists
            const existing = await this.databaseService.executeQuery(
                'SELECT id FROM game_files WHERE installation_id = ? AND file_path = ?',
                [gameFile.installationId, gameFile.filePath]
            );

            if (existing.length > 0) {
                // Update existing record
                await this.databaseService.executeQuery(`
                    UPDATE game_files 
                    SET file_hash = ?, pack_type = ?, file_name = ?
                    WHERE id = ?
                `, [gameFile.fileHash, gameFile.packType, gameFile.fileName, existing[0].id]);
                
                return existing[0].id;
            } else {
                // Insert new record
                const result = await this.databaseService.executeQuery(`
                    INSERT INTO game_files (installation_id, file_path, file_hash, pack_type, file_name)
                    VALUES (?, ?, ?, ?, ?)
                `, [gameFile.installationId, gameFile.filePath, gameFile.fileHash, gameFile.packType, gameFile.fileName]);

                return result.lastInsertRowid;
            }
        } catch (error) {
            this.logger.error('Error saving game file:', error);
            throw error;
        }
    }

    /**
     * Save game resources in batch for performance
     */
    private async saveGameResourcesBatch(resources: GameResource[]): Promise<void> {
        if (resources.length === 0) return;

        try {
            // Use transaction for batch insert
            this.databaseService.executeTransaction(() => {
                const stmt = this.databaseService.db.prepare(`
                    INSERT OR IGNORE INTO game_resources 
                    (game_file_id, resource_type, resource_group, resource_instance, tgi_key)
                    VALUES (?, ?, ?, ?, ?)
                `);

                for (const resource of resources) {
                    stmt.run([
                        resource.gameFileId,
                        resource.resourceType,
                        resource.resourceGroup,
                        resource.resourceInstance,
                        resource.tgiKey
                    ]);
                }
            });
        } catch (error) {
            this.logger.error('Error saving game resources batch:', error);
            throw error;
        }
    }

    /**
     * Get statistics about extracted base game resources
     */
    public async getExtractionStatistics(): Promise<{
        totalInstallations: number;
        totalFiles: number;
        totalResources: number;
        resourcesByType: { [type: string]: number };
    }> {
        try {
            const [installations, files, resources, resourcesByType] = await Promise.all([
                this.databaseService.executeQuery('SELECT COUNT(*) as count FROM game_installations WHERE is_valid = 1'),
                this.databaseService.executeQuery('SELECT COUNT(*) as count FROM game_files'),
                this.databaseService.executeQuery('SELECT COUNT(*) as count FROM game_resources'),
                this.databaseService.executeQuery(`
                    SELECT resource_type, COUNT(*) as count 
                    FROM game_resources 
                    GROUP BY resource_type 
                    ORDER BY count DESC 
                    LIMIT 20
                `)
            ]);

            const typeMap: { [type: string]: number } = {};
            for (const row of resourcesByType) {
                typeMap[`0x${row.resource_type.toString(16).toUpperCase()}`] = row.count;
            }

            return {
                totalInstallations: installations[0].count,
                totalFiles: files[0].count,
                totalResources: resources[0].count,
                resourcesByType: typeMap
            };
        } catch (error) {
            this.logger.error('Error getting extraction statistics:', error);
            return {
                totalInstallations: 0,
                totalFiles: 0,
                totalResources: 0,
                resourcesByType: {}
            };
        }
    }
}
