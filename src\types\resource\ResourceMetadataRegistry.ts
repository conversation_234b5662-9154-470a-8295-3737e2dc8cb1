/**
 * Resource Metadata Registry - Comprehensive Resource Type Metadata
 * 
 * This registry provides detailed metadata for all official Sims 4 resource types,
 * including categorization, file extensions, manager information, and validation rules.
 */

import { OfficialResourceType, ResourceCategory, ResourceTypeMetadata } from './OfficialResourceTypes.js';

/**
 * Comprehensive Resource Type Metadata Registry
 * Based on InstanceTuningDefinition system from the game
 */
export const RESOURCE_METADATA_REGISTRY: Map<OfficialResourceType, ResourceTypeMetadata> = new Map([
  // === STATIC RESOURCE TYPES ===
  [OfficialResourceType.INVALID, {
    typeName: 'invalid',
    resourceType: 4294967295,
    category: ResourceCategory.SYSTEM,
    fileExtension: 'invalid'
  }],
  
  [OfficialResourceType.MODEL, {
    typeName: 'model',
    resourceType: 23466547,
    category: ResourceCategory.MODEL,
    fileExtension: 'model'
  }],
  
  [OfficialResourceType.RIG, {
    typeName: 'rig',
    resourceType: 2393838558,
    category: ResourceCategory.MODEL,
    fileExtension: 'rig'
  }],
  
  [OfficialResourceType.FOOTPRINT, {
    typeName: 'footprint',
    resourceType: 3548561239,
    category: ResourceCategory.OBJECT,
    fileExtension: 'footprint'
  }],
  
  [OfficialResourceType.SLOT, {
    typeName: 'slot',
    resourceType: 3540272417,
    category: ResourceCategory.OBJECT,
    fileExtension: 'slot'
  }],
  
  [OfficialResourceType.OBJECTDEFINITION, {
    typeName: 'objectdefinition',
    resourceType: 3235601127,
    category: ResourceCategory.OBJECT,
    fileExtension: 'objectdefinition'
  }],
  
  [OfficialResourceType.OBJCATALOG, {
    typeName: 'objcatalog',
    resourceType: 832458525,
    category: ResourceCategory.OBJECT,
    fileExtension: 'objcatalog'
  }],
  
  [OfficialResourceType.PNG, {
    typeName: 'png',
    resourceType: 796721156,
    category: ResourceCategory.IMAGE,
    fileExtension: 'png'
  }],
  
  [OfficialResourceType.TGA, {
    typeName: 'tga',
    resourceType: 796721158,
    category: ResourceCategory.IMAGE,
    fileExtension: 'tga'
  }],
  
  [OfficialResourceType.DDS, {
    typeName: 'dds',
    resourceType: 11720834,
    category: ResourceCategory.IMAGE,
    fileExtension: 'dds'
  }],
  
  [OfficialResourceType.STATEMACHINE, {
    typeName: 'statemachine',
    resourceType: 47570707,
    category: ResourceCategory.ANIMATION,
    fileExtension: 'statemachine'
  }],
  
  [OfficialResourceType.CLIP, {
    typeName: 'clip',
    resourceType: 1797309683,
    category: ResourceCategory.ANIMATION,
    fileExtension: 'clip'
  }],
  
  [OfficialResourceType.WALKSTYLE, {
    typeName: 'walkstyle',
    resourceType: 666901909,
    category: ResourceCategory.ANIMATION,
    fileExtension: 'walkstyle'
  }],
  
  [OfficialResourceType.PROPX, {
    typeName: 'propx',
    resourceType: 968010314,
    category: ResourceCategory.SOUND,
    fileExtension: 'propx'
  }],
  
  [OfficialResourceType.XML, {
    typeName: 'xml',
    resourceType: 53690476,
    category: ResourceCategory.TUNING,
    fileExtension: 'xml'
  }],
  
  [OfficialResourceType.COMBINED_TUNING, {
    typeName: 'combined_tuning',
    resourceType: 1659456824,
    category: ResourceCategory.TUNING,
    fileExtension: 'combined_tuning'
  }],
  
  [OfficialResourceType.SIMINFO, {
    typeName: 'siminfo',
    resourceType: 39769844,
    category: ResourceCategory.GAMEPLAY,
    fileExtension: 'siminfo'
  }],
  
  [OfficialResourceType.CASPART, {
    typeName: 'caspart',
    resourceType: 55242443,
    category: ResourceCategory.CASPART,
    fileExtension: 'caspart'
  }],
  
  // === TUNING RESOURCE TYPES ===
  [OfficialResourceType.TUNING, {
    typeName: 'tuning',
    typeNamePlural: 'tuning',
    resourceType: 62078431,
    category: ResourceCategory.TUNING,
    fileExtension: 'tun',
    managerName: 'module_tuning_manager',
    managerType: 'MODULE_TUNING_MANAGER'
  }],
  
  [OfficialResourceType.SNIPPET, {
    typeName: 'snippet',
    resourceType: 2113017500,
    category: ResourceCategory.TUNING,
    fileExtension: 'snippet',
    requireReference: true
  }],
  
  [OfficialResourceType.POSTURE, {
    typeName: 'posture',
    resourceType: 2909789983,
    category: ResourceCategory.GAMEPLAY,
    fileExtension: 'posture'
  }],
  
  [OfficialResourceType.SLOT_TYPE, {
    typeName: 'slot_type',
    resourceType: 1772477092,
    category: ResourceCategory.OBJECT,
    fileExtension: 'slot_type',
    useGuidForRef: false,
    baseGameOnly: true,
    requireReference: true
  }],
  
  [OfficialResourceType.STATIC_COMMODITY, {
    typeName: 'static_commodity',
    typeNamePlural: 'static_commodities',
    resourceType: 1359443523,
    category: ResourceCategory.GAMEPLAY,
    fileExtension: 'scommodity',
    requireReference: true
  }],
  
  [OfficialResourceType.RELATIONSHIP_BIT, {
    typeName: 'relationship_bit',
    resourceType: 151314192,
    category: ResourceCategory.RELATIONSHIP,
    fileExtension: 'relbit',
    requireReference: true
  }],
  
  [OfficialResourceType.OBJECT_STATE, {
    typeName: 'object_state',
    resourceType: 1526890910,
    category: ResourceCategory.OBJECT,
    fileExtension: 'object_state',
    managerType: 'INSTANCED_CLASS_MANAGER'
  }],
  
  [OfficialResourceType.RECIPE, {
    typeName: 'recipe',
    resourceType: 3952605219,
    category: ResourceCategory.GAMEPLAY,
    fileExtension: 'recipe',
    managerType: 'INSTANCED_CLASS_MANAGER'
  }],
  
  [OfficialResourceType.GAME_RULESET, {
    typeName: 'game_ruleset',
    resourceType: 3779558936,
    category: ResourceCategory.GAMEPLAY,
    fileExtension: 'game_ruleset',
    requireReference: true
  }],
  
  [OfficialResourceType.STATISTIC, {
    typeName: 'statistic',
    resourceType: 865846717,
    category: ResourceCategory.GAMEPLAY,
    fileExtension: 'statistic',
    requireReference: true,
    managerType: 'STATISTIC_INSTANCE_MANAGER'
  }],
  
  [OfficialResourceType.MOOD, {
    typeName: 'mood',
    resourceType: 3128647864,
    category: ResourceCategory.MOOD,
    fileExtension: 'mood',
    requireReference: true
  }],
  
  [OfficialResourceType.BUFF, {
    typeName: 'buff',
    resourceType: 1612179606,
    category: ResourceCategory.BUFF,
    fileExtension: 'buff',
    requireReference: true,
    managerType: 'INSTANCED_CLASS_MANAGER'
  }],
  
  [OfficialResourceType.TRAIT, {
    typeName: 'trait',
    resourceType: 3412057543,
    category: ResourceCategory.TRAIT,
    fileExtension: 'trait'
  }],
  
  [OfficialResourceType.ASPIRATION, {
    typeName: 'aspiration',
    resourceType: 683034229,
    category: ResourceCategory.ASPIRATION,
    fileExtension: 'aspiration',
    managerType: 'ASPIRATION_INSTANCE_MANAGER'
  }],
  
  [OfficialResourceType.ASPIRATION_CATEGORY, {
    typeName: 'aspiration_category',
    typeNamePlural: 'aspiration_categories',
    resourceType: 3813727192,
    category: ResourceCategory.ASPIRATION,
    fileExtension: 'aspiration_category',
    requireReference: true
  }],
  
  [OfficialResourceType.CAREER, {
    typeName: 'career',
    resourceType: 1939434475,
    category: ResourceCategory.CAREER,
    fileExtension: 'career'
  }],
  
  [OfficialResourceType.CAREER_LEVEL, {
    typeName: 'career_level',
    resourceType: 745582072,
    category: ResourceCategory.CAREER,
    fileExtension: 'career_level',
    requireReference: true
  }],
  
  [OfficialResourceType.CAREER_TRACK, {
    typeName: 'career_track',
    resourceType: 1221024995,
    category: ResourceCategory.CAREER,
    fileExtension: 'career_track',
    requireReference: true
  }],
  
  [OfficialResourceType.INTERACTION, {
    typeName: 'interaction',
    resourceType: 3900887599,
    category: ResourceCategory.INTERACTION,
    fileExtension: 'interaction',
    managerName: 'affordance_manager',
    requireReference: true,
    managerType: 'INTERACTION_INSTANCE_MANAGER'
  }],
  
  [OfficialResourceType.ACHIEVEMENT, {
    typeName: 'achievement',
    resourceType: 2018877086,
    category: ResourceCategory.GAMEPLAY,
    fileExtension: 'achievement'
  }],
  
  [OfficialResourceType.SERVICE_NPC, {
    typeName: 'service_npc',
    resourceType: 2629964386,
    category: ResourceCategory.GAMEPLAY,
    fileExtension: 'service_npc',
    requireReference: true
  }],
  
  [OfficialResourceType.VENUE, {
    typeName: 'venue',
    resourceType: 3871070174,
    category: ResourceCategory.WORLD,
    fileExtension: 'venue'
  }],
  
  [OfficialResourceType.OBJECT, {
    typeName: 'object',
    resourceType: 3055412916,
    category: ResourceCategory.OBJECT,
    fileExtension: 'object',
    managerName: 'definition_manager',
    requireReference: true,
    managerType: 'DEFINITION_MANAGER'
  }],
  
  [OfficialResourceType.ANIMATION, {
    typeName: 'animation',
    resourceType: 3994535597,
    category: ResourceCategory.ANIMATION,
    fileExtension: 'animation',
    requireReference: true,
    managerType: 'INSTANCED_CLASS_MANAGER'
  }],
  
  [OfficialResourceType.ACTION, {
    typeName: 'action',
    resourceType: 209137191,
    category: ResourceCategory.GAMEPLAY,
    fileExtension: 'action',
    requireReference: true,
    managerType: 'INSTANCED_CLASS_MANAGER'
  }],
  
  [OfficialResourceType.SITUATION, {
    typeName: 'situation',
    resourceType: 4223905515,
    category: ResourceCategory.SITUATION,
    fileExtension: 'situation'
  }],
  
  [OfficialResourceType.SITUATION_JOB, {
    typeName: 'situation_job',
    resourceType: 2617738591,
    category: ResourceCategory.SITUATION,
    fileExtension: 'situation_job',
    requireReference: true
  }],
  
  [OfficialResourceType.SITUATION_GOAL, {
    typeName: 'situation_goal',
    resourceType: 1502554343,
    category: ResourceCategory.SITUATION,
    fileExtension: 'situation_goal',
    requireReference: true
  }],
  
  // === EXPANSION PACK CONTENT ===
  [OfficialResourceType.UNIVERSITY, {
    typeName: 'university',
    typeNamePlural: 'universities',
    resourceType: 3646477745,
    category: ResourceCategory.UNIVERSITY,
    fileExtension: 'university',
    requireReference: true
  }],
  
  [OfficialResourceType.UNIVERSITY_MAJOR, {
    typeName: 'university_major',
    resourceType: 660124491,
    category: ResourceCategory.UNIVERSITY,
    fileExtension: 'university_major',
    requireReference: true
  }],
  
  [OfficialResourceType.SEASON, {
    typeName: 'season',
    resourceType: 3381515358,
    category: ResourceCategory.SEASONS,
    fileExtension: 'season',
    requireReference: true
  }],
  
  [OfficialResourceType.SPELL, {
    typeName: 'spell',
    resourceType: 523506649,
    category: ResourceCategory.MAGIC,
    fileExtension: 'spell',
    requireReference: true
  }],
  
  [OfficialResourceType.CLAN, {
    typeName: 'clan',
    resourceType: 3737052837,
    category: ResourceCategory.SOCIAL,
    fileExtension: 'clan'
  }],
  
  // Default for unknown types
  [OfficialResourceType.UNKNOWN, {
    typeName: 'unknown',
    resourceType: 0,
    category: ResourceCategory.UNKNOWN,
    fileExtension: 'unknown'
  }]
]);

/**
 * Get metadata for a resource type
 */
export function getResourceMetadata(type: OfficialResourceType): ResourceTypeMetadata | undefined {
  return RESOURCE_METADATA_REGISTRY.get(type);
}

/**
 * Get all resource types in a category
 */
export function getResourceTypesByCategory(category: ResourceCategory): OfficialResourceType[] {
  const types: OfficialResourceType[] = [];
  for (const [type, metadata] of RESOURCE_METADATA_REGISTRY.entries()) {
    if (metadata.category === category) {
      types.push(type);
    }
  }
  return types;
}

/**
 * Check if a resource type requires references
 */
export function requiresReference(type: OfficialResourceType): boolean {
  const metadata = getResourceMetadata(type);
  return metadata?.requireReference || false;
}

/**
 * Check if a resource type is base game only
 */
export function isBaseGameOnly(type: OfficialResourceType): boolean {
  const metadata = getResourceMetadata(type);
  return metadata?.baseGameOnly || false;
}

/**
 * Get file extension for a resource type
 */
export function getFileExtension(type: OfficialResourceType): string {
  const metadata = getResourceMetadata(type);
  return metadata?.fileExtension || 'unknown';
}
