﻿// Corrected imports
import { BinaryResourceType } from './core.js';
import { ResourceMetadata } from './interfaces.js'; // Import from interfaces.js
import { ResourceCategory } from './enums.js'; // Import from enums.js

/**
 * Resource metadata collection interface
 */
// Note: This collection likely needs to be updated to use the ResourceMetadata from interfaces.js
// if it's intended to store those objects. For now, just removing the conflicting interface.
export interface ResourceMetadataCollection {
  entries: ResourceMetadata[];
  map: Map<string, ResourceMetadata>;
  add(metadata: ResourceMetadata): void;
  get(key: string): ResourceMetadata | undefined;
  has(key: string): boolean;
  remove(key: string): boolean;
  clear(): void;
  size(): number;
}

/**
 * Resource type metadata interface
 */
// This seems distinct, representing metadata about a *type* of resource, not an instance.
export interface ResourceTypeMetadata {
  type: BinaryResourceType;
  name: string;
  description: string;
  category: ResourceCategory; // Use imported enum
  version?: string;
  author?: string;
  dependencies?: string[];
  conflicts?: string[];
  customData?: {
    [key: string]: unknown;
  };
  // Additional properties needed by the application
  mimeTypes?: string[];
  isCompressible?: boolean;
  isEncryptable?: boolean;
  isModifiable?: boolean;
  hasCustomData?: boolean;
  customDataSchema?: {
    [key: string]: {
      type: string;
      description: string;
      required: boolean;
    };
  };
}
