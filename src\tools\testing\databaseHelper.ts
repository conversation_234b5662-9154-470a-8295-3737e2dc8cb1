/**
 * Database Helper Functions
 *
 * This module provides helper functions for creating and managing databases for testing.
 */

import { DatabaseService } from '../../services/databaseService.js';
import { Logger } from '../../utils/logging/logger.js';

/**
 * Create an in-memory database for testing
 * @param logger Optional logger instance
 * @returns Promise resolving to database service
 */
export async function createInMemoryDatabase(logger?: Logger): Promise<DatabaseService> {
    // Create logger if not provided
    const dbLogger = logger || new Logger('InMemoryDatabase', 'info');

    // Create database service
    const databaseService = new DatabaseService(':memory:', dbLogger);

    // Initialize database
    await databaseService.initialize();

    return databaseService;
}
