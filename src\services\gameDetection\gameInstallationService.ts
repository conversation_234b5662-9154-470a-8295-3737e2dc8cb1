/**
 * Game Installation Service
 * Detects and manages Sims 4 game installations for base game resource filtering
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../databaseService.js';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

export interface GameInstallation {
    id?: number;
    path: string;
    version: string;
    platform: 'steam' | 'origin' | 'ea_app' | 'unknown';
    detectedAt: Date;
    isValid: boolean;
}

export interface GameFile {
    id?: number;
    installationId: number;
    filePath: string;
    fileHash: string;
    packType: 'base' | 'ep' | 'gp' | 'sp';
    fileName: string;
}

export interface GameResource {
    id?: number;
    gameFileId: number;
    resourceType: number;
    resourceGroup: number;
    resourceInstance: string;
    tgiKey: string; // Combined TGI for quick lookup
}

export class GameInstallationService {
    private logger: Logger;
    private databaseService: DatabaseService;
    private initialized = false;

    constructor(databaseService: DatabaseService) {
        this.logger = new Logger('GameInstallationService');
        this.databaseService = databaseService;
    }

    /**
     * Initialize the service and create database tables
     */
    public async initialize(): Promise<void> {
        if (this.initialized) return;

        try {
            await this.createTables();
            this.initialized = true;
            this.logger.info('Game Installation Service initialized');
        } catch (error) {
            this.logger.error('Failed to initialize Game Installation Service:', error);
            throw error;
        }
    }

    /**
     * Create database tables for game detection
     */
    private async createTables(): Promise<void> {
        try {
            // Game installations table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS game_installations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    path TEXT NOT NULL UNIQUE,
                    version TEXT,
                    platform TEXT NOT NULL,
                    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_valid BOOLEAN DEFAULT 1
                )
            `);

            // Game files table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS game_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    installation_id INTEGER NOT NULL,
                    file_path TEXT NOT NULL,
                    file_hash TEXT,
                    pack_type TEXT NOT NULL,
                    file_name TEXT NOT NULL,
                    FOREIGN KEY (installation_id) REFERENCES game_installations(id),
                    UNIQUE(installation_id, file_path)
                )
            `);

            // Game resources table
            await this.databaseService.executeQuery(`
                CREATE TABLE IF NOT EXISTS game_resources (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    game_file_id INTEGER NOT NULL,
                    resource_type INTEGER NOT NULL,
                    resource_group INTEGER NOT NULL,
                    resource_instance TEXT NOT NULL,
                    tgi_key TEXT NOT NULL,
                    FOREIGN KEY (game_file_id) REFERENCES game_files(id)
                )
            `);

            // Create indices for performance
            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_game_resources_tgi ON game_resources(tgi_key)
            `);

            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_game_resources_type ON game_resources(resource_type)
            `);

            await this.databaseService.executeQuery(`
                CREATE INDEX IF NOT EXISTS idx_game_files_installation ON game_files(installation_id)
            `);

            this.logger.debug('Game detection database tables created successfully');
        } catch (error) {
            this.logger.error('Error creating game detection tables:', error);
            throw error;
        }
    }

    /**
     * Scan for Sims 4 game installations
     */
    public async scanForGameInstallations(): Promise<GameInstallation[]> {
        await this.initialize();

        const installations: GameInstallation[] = [];
        const commonPaths = this.getCommonInstallationPaths();

        this.logger.info(`Scanning ${commonPaths.length} common installation paths...`);

        for (const installPath of commonPaths) {
            try {
                if (fs.existsSync(installPath)) {
                    const installation = await this.validateGameInstallation(installPath);
                    if (installation) {
                        installations.push(installation);
                        this.logger.info(`Found valid Sims 4 installation at: ${installPath}`);
                    }
                }
            } catch (error) {
                this.logger.debug(`Error checking installation path ${installPath}:`, error);
            }
        }

        // Save found installations to database
        for (const installation of installations) {
            await this.saveGameInstallation(installation);
        }

        this.logger.info(`Found ${installations.length} valid Sims 4 installations`);
        return installations;
    }

    /**
     * Get common Sims 4 installation paths based on platform
     */
    private getCommonInstallationPaths(): string[] {
        const platform = os.platform();
        const paths: string[] = [];

        if (platform === 'win32') {
            // Windows paths
            const programFiles = process.env.ProgramFiles || 'C:\\Program Files';
            const programFilesX86 = process.env['ProgramFiles(x86)'] || 'C:\\Program Files (x86)';

            // Steam paths
            paths.push(
                path.join(programFiles, 'Steam', 'steamapps', 'common', 'The Sims 4'),
                path.join(programFilesX86, 'Steam', 'steamapps', 'common', 'The Sims 4'),
                'C:\\Program Files (x86)\\Steam\\steamapps\\common\\The Sims 4'
            );

            // Origin/EA App paths
            paths.push(
                path.join(programFiles, 'Origin Games', 'The Sims 4'),
                path.join(programFilesX86, 'Origin Games', 'The Sims 4'),
                path.join(programFiles, 'EA Games', 'The Sims 4'),
                path.join(programFilesX86, 'EA Games', 'The Sims 4')
            );

            // EA App paths
            paths.push(
                path.join(programFiles, 'EA App', 'games', 'The Sims 4'),
                path.join(programFilesX86, 'EA App', 'games', 'The Sims 4')
            );
        } else if (platform === 'darwin') {
            // macOS paths
            paths.push(
                '/Applications/The Sims 4.app',
                path.join(os.homedir(), 'Applications', 'The Sims 4.app')
            );
        }

        return paths;
    }

    /**
     * Validate if a path contains a valid Sims 4 installation
     */
    private async validateGameInstallation(installPath: string): Promise<GameInstallation | null> {
        try {
            // Check for essential game files
            const gameExecutable = this.findGameExecutable(installPath);
            if (!gameExecutable) {
                return null;
            }

            // Try to read game version
            const version = await this.getGameVersion(installPath);
            const platform = this.detectPlatform(installPath);

            return {
                path: installPath,
                version: version || 'unknown',
                platform,
                detectedAt: new Date(),
                isValid: true
            };
        } catch (error) {
            this.logger.debug(`Invalid game installation at ${installPath}:`, error);
            return null;
        }
    }

    /**
     * Find the game executable in the installation directory
     */
    private findGameExecutable(installPath: string): string | null {
        const possibleExecutables = [
            'Game\\Bin\\TS4_x64.exe',
            'Game\\Bin\\TS4.exe',
            'Contents\\MacOS\\The Sims 4'
        ];

        for (const exe of possibleExecutables) {
            const fullPath = path.join(installPath, exe);
            if (fs.existsSync(fullPath)) {
                return fullPath;
            }
        }

        return null;
    }

    /**
     * Get game version from GameVersion.txt
     */
    private async getGameVersion(installPath: string): Promise<string | null> {
        const versionFile = path.join(installPath, 'GameVersion.txt');
        
        try {
            if (fs.existsSync(versionFile)) {
                const content = fs.readFileSync(versionFile, 'utf8').trim();
                // Clean up version string
                return content.replace(/\0/g, '').replace(/[^0-9.]/g, '');
            }
        } catch (error) {
            this.logger.debug(`Error reading game version from ${versionFile}:`, error);
        }

        return null;
    }

    /**
     * Detect platform based on installation path
     */
    private detectPlatform(installPath: string): 'steam' | 'origin' | 'ea_app' | 'unknown' {
        const lowerPath = installPath.toLowerCase();
        
        if (lowerPath.includes('steam')) {
            return 'steam';
        } else if (lowerPath.includes('origin')) {
            return 'origin';
        } else if (lowerPath.includes('ea app') || lowerPath.includes('ea games')) {
            return 'ea_app';
        }
        
        return 'unknown';
    }

    /**
     * Save game installation to database
     */
    private async saveGameInstallation(installation: GameInstallation): Promise<number> {
        try {
            // Check if installation already exists
            const existing = await this.databaseService.executeQuery(
                'SELECT id FROM game_installations WHERE path = ?',
                [installation.path]
            );

            if (existing.length > 0) {
                // Update existing installation
                await this.databaseService.executeQuery(`
                    UPDATE game_installations 
                    SET version = ?, platform = ?, detected_at = CURRENT_TIMESTAMP, is_valid = ?
                    WHERE path = ?
                `, [installation.version, installation.platform, installation.isValid ? 1 : 0, installation.path]);
                
                return existing[0].id;
            } else {
                // Insert new installation
                const result = await this.databaseService.executeQuery(`
                    INSERT INTO game_installations (path, version, platform, is_valid)
                    VALUES (?, ?, ?, ?)
                `, [installation.path, installation.version, installation.platform, installation.isValid ? 1 : 0]);

                return result.lastInsertRowid;
            }
        } catch (error) {
            this.logger.error('Error saving game installation:', error);
            throw error;
        }
    }

    /**
     * Get all game installations from database
     */
    public async getGameInstallations(): Promise<GameInstallation[]> {
        await this.initialize();

        try {
            const rows = await this.databaseService.executeQuery(`
                SELECT id, path, version, platform, detected_at, is_valid
                FROM game_installations
                WHERE is_valid = 1
                ORDER BY detected_at DESC
            `);

            return rows.map((row: any) => ({
                id: row.id,
                path: row.path,
                version: row.version,
                platform: row.platform,
                detectedAt: new Date(row.detected_at),
                isValid: row.is_valid === 1
            }));
        } catch (error) {
            this.logger.error('Error getting game installations:', error);
            return [];
        }
    }

    /**
     * Check if a TGI key belongs to base game content
     */
    public async isBaseGameResource(tgiKey: string): Promise<boolean> {
        await this.initialize();

        try {
            const result = await this.databaseService.executeQuery(
                'SELECT 1 FROM game_resources WHERE tgi_key = ? LIMIT 1',
                [tgiKey]
            );

            return result.length > 0;
        } catch (error) {
            this.logger.error('Error checking if resource is base game:', error);
            return false;
        }
    }
}
