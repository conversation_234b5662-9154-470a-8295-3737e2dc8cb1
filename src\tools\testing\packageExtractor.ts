/**
 * Package Extractor
 *
 * This module provides functions for extracting resources from package files
 * using memory-efficient streaming techniques.
 */

import { DatabaseService } from '../../services/databaseService.js';
import { ResourceInfo } from '../../types/database.js';
import { Logger } from '../../utils/logging/logger.js';
import { PackageAnalyzer } from '../../services/analysis/packageAnalyzer.js';
import { resourceTypeRegistry } from '../../utils/resource/resourceTypeRegistry.js';
import { StreamingPackageReader } from '../../services/analysis/stream/compatibility/streamingPackageReaderCompat.js';
import { EnhancedBufferPool } from '../../utils/memory/enhancedBufferPool.js';
import EnhancedMemoryManager from '../../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker, ResourceType, ResourceState } from '../../utils/memory/resourceTracker.js';
import resourcePool from '../../utils/resource/resourcePoolManager.js';
import path from 'path';
import { formatBytes } from '../../utils/formatting/formatUtils.js';

/**
 * Extract resources from a package file using memory-efficient streaming techniques
 * @param packagePath Path to package file
 * @param databaseService Database service
 * @param logger Logger instance
 * @returns Promise resolving to array of extracted resources
 */
export async function extractResourcesFromPackage(
    packagePath: string,
    databaseService: DatabaseService,
    logger: Logger
): Promise<ResourceInfo[]> {
    // Track resources for proper cleanup
    const resourceTracker = ResourceTracker.getInstance();
    const memoryManager = EnhancedMemoryManager.getInstance();

    // Track this operation for memory management
    const operationId = `extract_${path.basename(packagePath)}_${Date.now()}`;
    memoryManager.trackResource('packageExtraction', operationId);

    // Log initial memory usage
    logMemoryUsage(logger, 'Before package extraction');

    try {
        // Initialize resource type registry
        resourceTypeRegistry.initialize();

        // Create buffer pool with adaptive sizing based on memory pressure
        const memoryPressure = memoryManager.getMemoryPressure();
        const bufferPoolSize = calculateAdaptiveBufferPoolSize(memoryPressure);

        const bufferPool = new EnhancedBufferPool({
            initialSize: bufferPoolSize.initialSize,
            maxSize: bufferPoolSize.maxSize,
            bufferSize: bufferPoolSize.bufferSize,
            logger
        });

        // Track buffer pool for cleanup
        resourceTracker.trackResource(
            ResourceType.BUFFER_POOL,
            'packageExtractor',
            async () => {
                bufferPool.clear();
            },
            {
                id: `buffer_pool_${operationId}`,
                state: ResourceState.ACTIVE,
                metadata: { packagePath }
            }
        );

        // Create streaming package reader with adaptive thresholds
        const streamingReader = new StreamingPackageReader(packagePath, {
            directBufferThreshold: calculateDirectBufferThreshold(memoryPressure),
            chunkedProcessingThreshold: calculateChunkedProcessingThreshold(memoryPressure),
            chunkSize: calculateChunkSize(memoryPressure),
            bufferPoolSize: bufferPoolSize.initialSize,
            maxBufferSize: bufferPoolSize.bufferSize
        });

        // Track streaming reader for cleanup
        resourceTracker.trackResource(
            ResourceType.FILE_READER,
            'packageExtractor',
            async () => {
                await streamingReader.close();
            },
            {
                id: `streaming_reader_${operationId}`,
                state: ResourceState.ACTIVE,
                metadata: { packagePath }
            }
        );

        // Create package analyzer
        const packageAnalyzer = new PackageAnalyzer(
            databaseService,
            logger
        );

        // Analyze package using the streaming approach
        logger.info(`Analyzing package ${packagePath} using streaming approach`);
        const packageInfo = await packageAnalyzer.analyzePackage(packagePath, {
            cleanupBuffers: true,
            batchSize: calculateBatchSize(memoryPressure)
        });

        // Log memory usage after analysis
        logMemoryUsage(logger, 'After package analysis');

        // Get resources from database
        const resources: ResourceInfo[] = [];

        if (packageInfo && packageInfo.id) {
            // Use resource pool to manage database query
            const result = await resourcePool.submit(
                `query_resources_${packageInfo.id}`,
                async () => {
                    return await databaseService.executeQuery(`
                        SELECT * FROM Resources WHERE packageId = ?
                    `, [packageInfo.id]);
                }
            );

            if (result && Array.isArray(result)) {
                for (const row of result) {
                    // Convert database row to ResourceInfo
                    const resourceInfo: ResourceInfo = {
                        id: row.id,
                        packageId: row.packageId,
                        key: {
                            type: row.type,
                            group: BigInt(row.group),
                            instance: BigInt(row.instance)
                        },
                        type: row.type,
                        group: row.group,
                        instance: row.instance,
                        resourceType: row.resourceType,
                        size: row.size,
                        offset: row.offset,
                        compressed: row.compressed === 1,
                        decompressedSize: row.decompressedSize,
                        deleted: row.deleted === 1,
                        metadata: row.metadata ? JSON.parse(row.metadata) : {},
                        contentSnippet: row.contentSnippet || '',
                        packagePath: packagePath
                    };

                    resources.push(resourceInfo);
                }

                logger.info(`Retrieved ${resources.length} resources from database for package ${packagePath}`);
            }
        }

        // Force garbage collection if available
        if (global.gc) {
            logger.debug('Running garbage collection after package extraction');
            global.gc();
        }

        // Log final memory usage
        logMemoryUsage(logger, 'After package extraction');

        return resources;
    } catch (error: any) {
        logger.error(`Error extracting resources from package ${packagePath}: ${error.message}`);
        throw error;
    } finally {
        // Untrack this operation
        memoryManager.untrackResource('packageExtraction', operationId);

        // Release all tracked resources for this operation
        await resourceTracker.releaseResourcesByOwner('packageExtractor');
    }
}

/**
 * Log current memory usage
 * @param logger Logger instance
 * @param label Label for the log entry
 */
function logMemoryUsage(logger: Logger, label: string): void {
    const memoryManager = EnhancedMemoryManager.getInstance();
    const memStats = memoryManager.getMemoryStats();

    logger.info(`[Memory Usage] ${label}: Heap used ${formatBytes(memStats.heapUsed)} / ${formatBytes(memStats.heapTotal)} (${Math.round(memStats.usedPercentage)}%), RSS: ${formatBytes(memStats.rss)}`);
}

/**
 * Calculate adaptive buffer pool size based on memory pressure
 * @param memoryPressure Current memory pressure (0-1)
 * @returns Buffer pool size configuration
 */
function calculateAdaptiveBufferPoolSize(memoryPressure: number): { initialSize: number, maxSize: number, bufferSize: number } {
    if (memoryPressure > 0.8) {
        // High memory pressure - very conservative
        return {
            initialSize: 3,
            maxSize: 10,
            bufferSize: 256 * 1024 // 256KB
        };
    } else if (memoryPressure > 0.6) {
        // Medium memory pressure - conservative
        return {
            initialSize: 5,
            maxSize: 20,
            bufferSize: 512 * 1024 // 512KB
        };
    } else {
        // Low memory pressure - normal
        return {
            initialSize: 10,
            maxSize: 50,
            bufferSize: 1024 * 1024 // 1MB
        };
    }
}

/**
 * Calculate direct buffer threshold based on memory pressure
 * @param memoryPressure Current memory pressure (0-1)
 * @returns Direct buffer threshold in bytes
 */
function calculateDirectBufferThreshold(memoryPressure: number): number {
    if (memoryPressure > 0.8) {
        return 256 * 1024; // 256KB
    } else if (memoryPressure > 0.6) {
        return 512 * 1024; // 512KB
    } else {
        return 1024 * 1024; // 1MB
    }
}

/**
 * Calculate chunked processing threshold based on memory pressure
 * @param memoryPressure Current memory pressure (0-1)
 * @returns Chunked processing threshold in bytes
 */
function calculateChunkedProcessingThreshold(memoryPressure: number): number {
    if (memoryPressure > 0.8) {
        return 2 * 1024 * 1024; // 2MB
    } else if (memoryPressure > 0.6) {
        return 5 * 1024 * 1024; // 5MB
    } else {
        return 10 * 1024 * 1024; // 10MB
    }
}

/**
 * Calculate chunk size based on memory pressure
 * @param memoryPressure Current memory pressure (0-1)
 * @returns Chunk size in bytes
 */
function calculateChunkSize(memoryPressure: number): number {
    if (memoryPressure > 0.8) {
        return 16 * 1024; // 16KB
    } else if (memoryPressure > 0.6) {
        return 32 * 1024; // 32KB
    } else {
        return 64 * 1024; // 64KB
    }
}

/**
 * Calculate batch size based on memory pressure
 * @param memoryPressure Current memory pressure (0-1)
 * @returns Batch size
 */
function calculateBatchSize(memoryPressure: number): number {
    if (memoryPressure > 0.8) {
        return 5;
    } else if (memoryPressure > 0.6) {
        return 10;
    } else {
        return 20;
    }
}