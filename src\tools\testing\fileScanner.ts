/**
 * File scanner utilities for test scripts
 *
 * This module provides utilities for scanning directories for Sims 4 mod files.
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { createPrintFunction } from '../../utils/console/consoleOutput.js';

// Create a print function for direct output
const print = createPrintFunction();

/**
 * Find package files in a directory and its subdirectories with options
 * @param directory Directory to scan
 * @param options Scan options
 * @returns Array of package file paths
 */
export async function findPackageFiles(
    directory: string,
    options: {
        maxFiles?: number;
        maxDepth?: number;
        skipFolders?: string[];
        progressCallback?: (count: number, path: string) => void;
        randomize?: boolean;
    } = {}
): Promise<string[]> {
    // Default options
    const maxFiles = options.maxFiles || Number.MAX_SAFE_INTEGER;
    const maxDepth = options.maxDepth || Number.MAX_SAFE_INTEGER;
    const skipFolders = options.skipFolders || ['_Backup', 'cache', 'Cache', 'Backups'];
    const progressCallback = options.progressCallback;
    const randomize = options.randomize || false;

    // Result array
    const packageFiles: string[] = [];

    // Internal recursive function with depth tracking
    async function scanDirectory(dir: string, currentDepth: number): Promise<void> {
        // Stop if we've reached the maximum files or depth
        if (packageFiles.length >= maxFiles || currentDepth > maxDepth) {
            return;
        }

        try {
            // Read directory contents
            const entries = await fs.readdir(dir, { withFileTypes: true });

            // Process files first (for faster results)
            for (const entry of entries) {
                // Stop if we've reached the maximum files
                if (packageFiles.length >= maxFiles) {
                    return;
                }

                const fullPath = path.join(dir, entry.name);

                if (entry.isFile() && entry.name.toLowerCase().endsWith('.package')) {
                    // Add package file
                    packageFiles.push(fullPath);

                    // Call progress callback if provided
                    if (progressCallback) {
                        progressCallback(packageFiles.length, fullPath);
                    }
                }
            }

            // Then process directories
            for (const entry of entries) {
                // Stop if we've reached the maximum files
                if (packageFiles.length >= maxFiles) {
                    return;
                }

                const fullPath = path.join(dir, entry.name);

                if (entry.isDirectory()) {
                    // Skip specified folders
                    if (skipFolders.some(folder => entry.name.toLowerCase() === folder.toLowerCase())) {
                        continue;
                    }

                    // Skip folders that start with underscore (common for backup folders)
                    if (entry.name.startsWith('_')) {
                        continue;
                    }

                    // Recursively scan subdirectory
                    await scanDirectory(fullPath, currentDepth + 1);
                }
            }
        } catch (error: any) {
            print(`Error scanning directory ${dir}: ${error.message || error}`);
        }
    }

    // Start scanning from the root directory
    await scanDirectory(directory, 0);

    // Randomize results if requested
    if (randomize && packageFiles.length > 0) {
        // Fisher-Yates shuffle
        for (let i = packageFiles.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [packageFiles[i], packageFiles[j]] = [packageFiles[j], packageFiles[i]];
        }
    }

    return packageFiles;
}

/**
 * Find TS4Script files in a directory and its subdirectories with options
 * @param directory Directory to scan
 * @param options Scan options
 * @returns Array of TS4Script file paths
 */
export async function findTS4ScriptFiles(
    directory: string,
    options: {
        maxFiles?: number;
        maxDepth?: number;
        skipFolders?: string[];
        progressCallback?: (count: number, path: string) => void;
        randomize?: boolean;
    } = {}
): Promise<string[]> {
    // Default options
    const maxFiles = options.maxFiles || Number.MAX_SAFE_INTEGER;
    const maxDepth = options.maxDepth || Number.MAX_SAFE_INTEGER;
    const skipFolders = options.skipFolders || ['_Backup', 'cache', 'Cache', 'Backups'];
    const progressCallback = options.progressCallback;
    const randomize = options.randomize || false;

    // Result array
    const ts4ScriptFiles: string[] = [];

    // Internal recursive function with depth tracking
    async function scanDirectory(dir: string, currentDepth: number): Promise<void> {
        // Stop if we've reached the maximum files or depth
        if (ts4ScriptFiles.length >= maxFiles || currentDepth > maxDepth) {
            return;
        }

        try {
            // Read directory contents
            const entries = await fs.readdir(dir, { withFileTypes: true });

            // Process files first (for faster results)
            for (const entry of entries) {
                // Stop if we've reached the maximum files
                if (ts4ScriptFiles.length >= maxFiles) {
                    return;
                }

                const fullPath = path.join(dir, entry.name);

                if (entry.isFile() && entry.name.toLowerCase().endsWith('.ts4script')) {
                    // Add TS4Script file
                    ts4ScriptFiles.push(fullPath);

                    // Call progress callback if provided
                    if (progressCallback) {
                        progressCallback(ts4ScriptFiles.length, fullPath);
                    }
                }
            }

            // Then process directories
            for (const entry of entries) {
                // Stop if we've reached the maximum files
                if (ts4ScriptFiles.length >= maxFiles) {
                    return;
                }

                const fullPath = path.join(dir, entry.name);

                if (entry.isDirectory()) {
                    // Skip specified folders
                    if (skipFolders.some(folder => entry.name.toLowerCase() === folder.toLowerCase())) {
                        continue;
                    }

                    // Skip folders that start with underscore (common for backup folders)
                    if (entry.name.startsWith('_')) {
                        continue;
                    }

                    // Recursively scan subdirectory
                    await scanDirectory(fullPath, currentDepth + 1);
                }
            }
        } catch (error: any) {
            print(`Error scanning directory ${dir} for TS4Script files: ${error.message || error}`);
        }
    }

    // Start scanning from the root directory
    await scanDirectory(directory, 0);

    // Randomize results if requested
    if (randomize && ts4ScriptFiles.length > 0) {
        // Fisher-Yates shuffle
        for (let i = ts4ScriptFiles.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [ts4ScriptFiles[i], ts4ScriptFiles[j]] = [ts4ScriptFiles[j], ts4ScriptFiles[i]];
        }
    }

    return ts4ScriptFiles;
}
