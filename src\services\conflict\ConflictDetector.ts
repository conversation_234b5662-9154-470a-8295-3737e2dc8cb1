import { Logger } from '../../utils/logging/logger.js';
import { ResourceInfo } from '../../types/database.js';
import { ConflictInfo, ConflictSeverity, ConflictType, ConflictDetectionResult as ConflictDetectionResultType } from '../../types/conflict/index.js';
import { DatabaseService } from '../databaseService.js';
import { ConflictDetectorBase } from './ConflictDetectorBase.js';
import { TGIConflictDetector, TGIConflictDetectionOptions } from './detectors/TGIConflictDetector.js';
import { ContentConflictDetector, ContentConflictDetectionOptions } from './detectors/ContentConflictDetector.js';
import { ScriptConflictDetector, ScriptConflictDetectionOptions } from './detectors/ScriptConflictDetector.js';
import { SemanticConflictDetector } from './detectors/SemanticConflictDetector.js';
import { LSHConflictDetector, LSHConflictDetectionOptions } from './detectors/LSHConflictDetector.js';
import { ConflictSeverityCalculator } from './ConflictSeverityCalculator.js';
import { ContextAwareAnalyzer } from '../analysis/semantic/contextAwareAnalyzer.js';
import { GameplaySystemRegistry } from '../analysis/semantic/gameplaySystemRegistry.js';
import { ResourcePurposeAnalyzer } from '../analysis/semantic/resourcePurposeAnalyzer.js';
import { DependencyGraphBuilder } from '../analysis/semantic/dependencyGraph/dependencyGraphBuilder.js';
import { DependencyGraphAnalyzer } from '../analysis/semantic/dependencyGraph/dependencyGraphAnalyzer.js';
import EnhancedMemoryManager from '../../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker, ResourceType, ResourceState, CleanupPriority } from '../../utils/memory/resourceTracker.js';
import { formatBytes } from '../../utils/formatting/formatUtils.js';

/**
 * Options for conflict detection
 */
export interface ConflictDetectionOptions {
    /**
     * Options for TGI conflict detection
     */
    tgiOptions?: TGIConflictDetectionOptions;

    /**
     * Options for content conflict detection
     */
    contentOptions?: ContentConflictDetectionOptions;

    /**
     * Options for script conflict detection
     */
    scriptOptions?: ScriptConflictDetectionOptions;

    /**
     * Options for LSH conflict detection
     */
    lshOptions?: LSHConflictDetectionOptions;

    /**
     * Options for dependency graph building
     */
    dependencyGraphOptions?: {
        /**
         * Whether to include weak dependencies
         * Default: true
         */
        includeWeakDependencies?: boolean;

        /**
         * Whether to include resource metadata
         * Default: true
         */
        includeResourceMetadata?: boolean;

        /**
         * Maximum depth for dependency chains
         * Default: 5
         */
        maxDepth?: number;
    };

    /**
     * Whether to enable content conflict detection
     * Default: true
     */
    enableContentDetection?: boolean;

    /**
     * Whether to enable script conflict detection
     * Default: true
     */
    enableScriptDetection?: boolean;

    /**
     * Whether to enable semantic conflict detection
     * Default: true
     */
    enableSemanticDetection?: boolean;

    /**
     * Whether to enable LSH conflict detection
     * Default: true
     */
    enableLSHDetection?: boolean;

    /**
     * Whether to enable dependency graph analysis
     * Default: true
     */
    enableDependencyGraphAnalysis?: boolean;

    /**
     * Maximum number of resources to compare
     * Default: 1000
     */
    maxResourcesToCompare?: number;

    /**
     * Maximum number of conflicts to return
     * Default: 100
     */
    maxConflictsToReturn?: number;

    /**
     * Whether to sort conflicts by severity
     * Default: true
     */
    sortBySeverity?: boolean;

    /**
     * Whether to deduplicate conflicts
     * Default: true
     */
    deduplicateConflicts?: boolean;
}

/**
 * Result of conflict detection
 */
export interface ConflictDetectionResult extends ConflictDetectionResultType {
    /**
     * Array of detected conflicts
     */
    conflicts: ConflictInfo[];

    /**
     * Number of resources compared
     */
    resourcesCompared: number;

    /**
     * Time taken for conflict detection (in milliseconds)
     */
    timeTaken: number;

    /**
     * Breakdown of conflicts by severity
     */
    severityBreakdown: {
        [key in ConflictSeverity]: number;
    };

    /**
     * Breakdown of conflicts by type
     */
    typeBreakdown: {
        [key in ConflictType]: number;
    };
}

/**
 * Main conflict detector that uses specialized detectors
 */
export class ConflictDetector {
    private logger: Logger;
    private databaseService: DatabaseService;
    private conflictRepository: any;
    private options: ConflictDetectionOptions;
    private detectors: ConflictDetectorBase[];
    private severityCalculator: ConflictSeverityCalculator;
    private semanticServices?: {
        contextAwareAnalyzer: ContextAwareAnalyzer;
        gameplaySystemRegistry: GameplaySystemRegistry;
        resourcePurposeAnalyzer: ResourcePurposeAnalyzer;
    };
    private dependencyGraphServices?: {
        dependencyGraphBuilder: DependencyGraphBuilder;
        dependencyGraphAnalyzer: DependencyGraphAnalyzer;
    };

    /**
     * Create a new conflict detector
     * @param databaseService Database service instance
     * @param options Options for conflict detection
     * @param logger Optional logger instance
     */
    constructor(
        databaseService: DatabaseService,
        options: ConflictDetectionOptions = {},
        logger?: Logger
    ) {
        this.databaseService = databaseService;
        this.conflictRepository = databaseService.conflicts;
        this.logger = logger || new Logger('ConflictDetector');

        // Set default options
        this.options = {
            tgiOptions: options.tgiOptions || {},
            contentOptions: options.contentOptions || {},
            scriptOptions: options.scriptOptions || {},
            lshOptions: options.lshOptions || {},
            dependencyGraphOptions: options.dependencyGraphOptions || {
                includeWeakDependencies: true,
                includeResourceMetadata: true,
                maxDepth: 5
            },
            enableContentDetection: options.enableContentDetection !== false,
            enableScriptDetection: options.enableScriptDetection !== false,
            enableSemanticDetection: options.enableSemanticDetection !== false,
            enableLSHDetection: options.enableLSHDetection !== false,
            enableDependencyGraphAnalysis: options.enableDependencyGraphAnalysis !== false,
            maxResourcesToCompare: options.maxResourcesToCompare || 1000,
            maxConflictsToReturn: options.maxConflictsToReturn || 100,
            sortBySeverity: options.sortBySeverity !== false,
            deduplicateConflicts: options.deduplicateConflicts !== false
        };

        // Create severity calculator
        this.severityCalculator = new ConflictSeverityCalculator({}, this.logger);

        // Create specialized detectors
        this.detectors = [
            new TGIConflictDetector(databaseService, this.options.tgiOptions, this.logger)
        ];

        // Add content conflict detector if enabled
        if (this.options.enableContentDetection) {
            // Create a logger with debug level if logComparisonDetails is enabled
            let contentLogger = this.logger;
            if (this.options.contentOptions?.logComparisonDetails) {
                contentLogger = new Logger('ContentConflictDetector', 'debug');
            }

            this.detectors.push(
                new ContentConflictDetector(databaseService, this.options.contentOptions, contentLogger)
            );
            this.logger.debug('Content conflict detector added');
        }

        // Add script conflict detector if enabled
        if (this.options.enableScriptDetection) {
            const scriptDetector = new ScriptConflictDetector(databaseService, this.options.scriptOptions, this.logger);
            this.detectors.push(scriptDetector);
            this.logger.debug('Script conflict detector added');
        }

        // Add LSH conflict detector if enabled
        if (this.options.enableLSHDetection) {
            const lshDetector = new LSHConflictDetector(databaseService, this.options.lshOptions, this.logger);
            this.detectors.push(lshDetector);
            this.logger.debug('LSH conflict detector added');
        }

        // Store semantic services for later initialization
        if (this.options.enableSemanticDetection) {
            this.semanticServices = {
                contextAwareAnalyzer: new ContextAwareAnalyzer(databaseService, this.logger),
                gameplaySystemRegistry: new GameplaySystemRegistry(databaseService, this.logger),
                resourcePurposeAnalyzer: new ResourcePurposeAnalyzer(databaseService, this.logger)
            };
        }

        // Store dependency graph services for later initialization
        if (this.options.enableDependencyGraphAnalysis) {
            this.dependencyGraphServices = {
                dependencyGraphBuilder: new DependencyGraphBuilder(databaseService),
                dependencyGraphAnalyzer: new DependencyGraphAnalyzer(databaseService)
            };
        }

        this.logger.debug(`ConflictDetector created with ${this.detectors.length} specialized detectors`);
    }

    /**
     * Initialize the conflict detector and its specialized detectors
     */
    public async initialize(): Promise<void> {
        // Initialize script detector if it was added
        if (this.options.enableScriptDetection) {
            const scriptDetector = this.detectors.find(d => d instanceof ScriptConflictDetector);
            if (scriptDetector) {
                try {
                    await scriptDetector.initialize();
                    this.logger.debug('Script conflict detector initialized');
                } catch (error: any) {
                    this.logger.error(`Error initializing script conflict detector: ${error.message || error}`);
                }
            }
        }

        // Initialize LSH detector if it was added
        if (this.options.enableLSHDetection) {
            const lshDetector = this.detectors.find(d => d instanceof LSHConflictDetector);
            if (lshDetector) {
                try {
                    await lshDetector.initialize();
                    this.logger.debug('LSH conflict detector initialized');
                } catch (error: any) {
                    this.logger.error(`Error initializing LSH conflict detector: ${error.message || error}`);
                }
            }
        }

        // Initialize dependency graph services if enabled
        if (this.options.enableDependencyGraphAnalysis && this.dependencyGraphServices) {
            try {
                // Build the dependency graph
                await this.dependencyGraphServices.dependencyGraphBuilder.buildGraph({
                    includeWeakDependencies: this.options.dependencyGraphOptions?.includeWeakDependencies !== false,
                    includeResourceMetadata: this.options.dependencyGraphOptions?.includeResourceMetadata !== false,
                    maxDepth: this.options.dependencyGraphOptions?.maxDepth || 5
                });
                this.logger.debug('Dependency graph built successfully');
            } catch (error: any) {
                this.logger.error(`Error initializing dependency graph services: ${error.message || error}`);
            }
        }

        // Initialize semantic services and detector if enabled
        if (this.options.enableSemanticDetection && this.semanticServices) {
            try {
                // Initialize the services
                await Promise.all([
                    this.semanticServices.contextAwareAnalyzer.initialize(),
                    this.semanticServices.gameplaySystemRegistry.initialize(),
                    this.semanticServices.resourcePurposeAnalyzer.initialize()
                ]);
                this.logger.debug('Semantic services initialized');

                // Create and add the semantic conflict detector
                const semanticDetector = new SemanticConflictDetector(
                    this.databaseService,
                    this.semanticServices.contextAwareAnalyzer,
                    this.semanticServices.gameplaySystemRegistry,
                    this.semanticServices.resourcePurposeAnalyzer,
                    this.dependencyGraphServices?.dependencyGraphAnalyzer, // Pass the dependency graph analyzer
                    this.logger
                );

                // Initialize the semantic detector
                await semanticDetector.initialize();
                this.logger.debug('Semantic conflict detector initialized');
                this.detectors.push(semanticDetector);
                this.logger.debug('Semantic conflict detector added');
            } catch (error: any) {
                this.logger.error(`Error initializing semantic services or detector: ${error.message || error}`);
            }
        }

        this.logger.debug(`ConflictDetector initialized with ${this.detectors.length} specialized detectors`);
    }

    /**
     * Detect conflicts between resources with comprehensive memory management and streaming
     * @param resources Array of resources to check for conflicts
     * @returns Result of conflict detection
     */
    async detectConflicts(resources: ResourceInfo[]): Promise<ConflictDetectionResult> {
        // Initialize memory management
        const memoryManager = EnhancedMemoryManager.getInstance();
        const resourceTracker = ResourceTracker.getInstance();

        // Create a unique operation ID for this conflict detection run
        const operationId = `conflict_detection_${Date.now()}`;

        // Track this operation for memory management
        memoryManager.trackResource('conflictDetection', 1);

        // Track the operation with ResourceTracker for proper cleanup
        const operationResourceId = resourceTracker.trackResource(
            ResourceType.OPERATION,
            'ConflictDetector',
            async () => {
                // Cleanup function will be called when the resource is released
                this.logger.debug(`Cleaning up resources for conflict detection operation ${operationId}`);

                // Force garbage collection if available
                if (global.gc) {
                    global.gc();
                }
            },
            {
                id: operationId,
                state: ResourceState.ACTIVE,
                priority: CleanupPriority.HIGH,
                metadata: {
                    resourceCount: resources?.length || 0,
                    timestamp: Date.now()
                }
            }
        );

        const startTime = Date.now();
        const conflictBatchSize = 100; // Number of conflicts to accumulate before storing
        let totalConflicts = 0;

        // Log initial memory state
        this.logger.info(`Memory usage before conflict detection: ${this.formatMemoryUsage()}`);

        try {
            // Validate input resources
            if (!resources || !Array.isArray(resources)) {
                this.logger.error(`Invalid resources parameter: ${typeof resources}`);
                return {
                    conflicts: [],
                    resourcesCompared: 0,
                    timeTaken: Date.now() - startTime,
                    severityBreakdown: this.calculateSeverityBreakdown([]),
                    typeBreakdown: this.calculateTypeBreakdown([])
                };
            }

            // Filter out invalid resources
            const validResources = resources.filter(resource => {
                // Check if resource is an object
                if (!resource || typeof resource !== 'object') {
                    this.logger.warn(`Invalid resource: ${resource}`);
                    return false;
                }

                // Check if resource has required properties
                if (resource.type === undefined || resource.group === undefined || resource.instance === undefined) {
                    this.logger.warn(`Resource missing required properties: ${JSON.stringify(resource)}`);
                    return false;
                }

                // Check if resource has key property
                if (!resource.key) {
                    // Create key property if missing
                    resource.key = {
                        type: resource.type,
                        group: resource.group,
                        instance: resource.instance
                    };
                    this.logger.debug(`Created key property for resource: ${resource.id}`);
                }

                // Check if resource has metadata property
                if (!resource.metadata) {
                    // Create metadata property if missing
                    resource.metadata = {
                        name: resource.resourceType || 'Unknown Resource',
                        path: resource.packagePath || '',
                        hash: resource.hash || '',
                        size: resource.size || 0,
                        timestamp: Date.now()
                    };
                    this.logger.debug(`Created metadata property for resource: ${resource.id}`);
                }

                return true;
            });

            if (validResources.length === 0) {
                this.logger.warn(`No valid resources to check for conflicts`);
                return {
                    conflicts: [],
                    resourcesCompared: 0,
                    timeTaken: Date.now() - startTime,
                    severityBreakdown: this.calculateSeverityBreakdown([]),
                    typeBreakdown: this.calculateTypeBreakdown([])
                };
            }

            this.logger.info(`Validated ${validResources.length} resources out of ${resources.length}`);

            // Filter resources to focus on conflict-prone types
            const conflictProneTypes = [
                'TUNING_XML', 'SIMDATA', 'OBJDEF', 'STBL', 'STRING_TABLE',
                'MODL', 'MLOD', 'MTST', 'FTPT', 'BGEO', 'GEOM',
                'CASP', 'COBJ', 'TOBJ', 'SWRM', 'SLOT', 'BUFF'
            ];

            // Filter resources by type if we have a large dataset
            let resourcesToCheck = validResources;
            if (validResources.length > 1000) {
                resourcesToCheck = validResources.filter(r =>
                    r.resourceType && conflictProneTypes.includes(r.resourceType)
                );
                this.logger.info(`Filtered ${validResources.length} resources to ${resourcesToCheck.length} conflict-prone resources`);
            }

            // Limit the number of resources to compare
            resourcesToCheck = resourcesToCheck.slice(0, this.options.maxResourcesToCompare);
            this.logger.info(`Detecting conflicts among ${resourcesToCheck.length} resources (${resourcesToCheck.length > 0 ? resourcesToCheck[0].resourceType || 'unknown type' : 'no resources'})`);

            // Calculate total number of resource pairs to compare
            const totalPairs = (resourcesToCheck.length * (resourcesToCheck.length - 1)) / 2;
            this.logger.debug(`Total resource pairs to compare: ${totalPairs}`);

            // Track resources for memory management
            for (let i = 0; i < resourcesToCheck.length; i++) {
                const resource = resourcesToCheck[i];
                const resourceSize = this.calculateResourceSize(resource);

                // Track resource with memory manager
                memoryManager.trackResource('resource', resourceSize);

                // Track with resource tracker for proper cleanup
                resourceTracker.trackResource(
                    ResourceType.RESOURCE_INFO,
                    'ConflictDetector',
                    () => {
                        // No cleanup needed, just tracking
                    },
                    {
                        id: `resource_${resource.id || i}`,
                        size: resourceSize,
                        state: ResourceState.ACTIVE,
                        priority: CleanupPriority.MEDIUM,
                        metadata: {
                            operationId,
                            resourceType: resource.resourceType || 'unknown',
                            packagePath: resource.packagePath || 'unknown'
                        }
                    }
                );
            }

            // Group resources by package path to understand the distribution
            const packageResourceCounts = new Map<string, number>();
            for (const resource of resourcesToCheck) {
                const packagePath = resource.packagePath || 'unknown';
                packageResourceCounts.set(packagePath, (packageResourceCounts.get(packagePath) || 0) + 1);
            }

            // Log package resource counts
            this.logger.info(`Resource distribution by package:`);
            for (const [packagePath, count] of packageResourceCounts.entries()) {
                this.logger.info(`  ${packagePath}: ${count} resources`);
            }

            // Determine initial batch size based on memory pressure
            let resourceBatchSize = this.calculateAdaptiveBatchSize(memoryManager.getMemoryPressure());
            this.logger.info(`Initial resource batch size: ${resourceBatchSize} (memory pressure: ${(memoryManager.getMemoryPressure() * 100).toFixed(1)}%)`);

            // Run each detector
            for (const detector of this.detectors) {
                try {
                    this.logger.debug(`Running detector: ${detector.constructor.name}`);

                    // Track detector usage
                    const detectorId = `detector_${detector.constructor.name}_${Date.now()}`;
                    resourceTracker.trackResource(
                        ResourceType.DETECTOR,
                        'ConflictDetector',
                        async () => {
                            // Cleanup function - nothing specific needed
                        },
                        {
                            id: detectorId,
                            state: ResourceState.ACTIVE,
                            priority: CleanupPriority.HIGH,
                            metadata: {
                                operationId,
                                detectorType: detector.constructor.name
                            }
                        }
                    );

                    // Process resources in batches with adaptive sizing
                    const totalBatches = Math.ceil(resourcesToCheck.length / resourceBatchSize);

                    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                        // Check memory pressure and adjust batch size if needed
                        const currentMemoryPressure = memoryManager.getMemoryPressure();
                        const newBatchSize = this.calculateAdaptiveBatchSize(currentMemoryPressure);

                        if (newBatchSize !== resourceBatchSize) {
                            this.logger.info(`Adjusting batch size from ${resourceBatchSize} to ${newBatchSize} based on memory pressure (${(currentMemoryPressure * 100).toFixed(1)}%)`);
                            resourceBatchSize = newBatchSize;
                        }

                        const batchStart = batchIndex * resourceBatchSize;
                        const batchEnd = Math.min(batchStart + resourceBatchSize, resourcesToCheck.length);
                        const batchResources = resourcesToCheck.slice(batchStart, batchEnd);

                        // Track this batch
                        const batchId = `batch_${batchIndex}_${Date.now()}`;
                        resourceTracker.trackResource(
                            ResourceType.BATCH,
                            'ConflictDetector',
                            async () => {
                                // Cleanup function for batch
                                this.logger.debug(`Cleaning up batch ${batchIndex + 1}/${totalBatches}`);
                            },
                            {
                                id: batchId,
                                state: ResourceState.ACTIVE,
                                priority: CleanupPriority.HIGH,
                                metadata: {
                                    operationId,
                                    batchIndex,
                                    resourceCount: batchResources.length
                                }
                            }
                        );

                        this.logger.debug(`Processing batch ${batchIndex + 1}/${totalBatches} with ${batchResources.length} resources`);
                        const batchStartTime = Date.now();

                        // Process each resource pair in the batch
                        let batchConflicts: ConflictInfo[] = [];

                        for (let i = 0; i < batchResources.length; i++) {
                            const resource1 = batchResources[i];

                            // Compare with all resources (including those from previous batches)
                            for (let j = 0; j < resourcesToCheck.length; j++) {
                                // Skip if it's the same resource or we've already compared this pair
                                if (batchStart + i === j) continue;

                                // Skip if resources are from the same package (only compare resources from different mods)
                                if (resource1.packagePath === resourcesToCheck[j].packagePath) continue;

                                const resource2 = resourcesToCheck[j];

                                try {
                                    // Track this comparison
                                    const comparisonId = `comparison_${resource1.id}_${resource2.id}`;
                                    memoryManager.trackResource('comparison', 1);

                                    // Detect conflicts between these two resources
                                    let detectorConflicts: ConflictInfo[] = [];

                                    // Check if the detector is SemanticConflictDetector
                                    if (detector.constructor.name === 'SemanticConflictDetector') {
                                        // For SemanticConflictDetector, pass resource IDs
                                        if (resource1.id !== undefined && resource2.id !== undefined) {
                                            detectorConflicts = await detector.detectConflicts(resource1.id, resource2.id);
                                        }
                                    } else {
                                        try {
                                            // For other detectors, pass the entire resource objects
                                            // Check if the detector has a detectConflict method that takes two resources
                                            if (typeof detector.detectConflict === 'function') {
                                                const conflict = await detector.detectConflict(resource1, resource2);
                                                if (conflict) {
                                                    detectorConflicts = [conflict];
                                                }
                                            } else if (typeof detector.detectConflicts === 'function') {
                                                // Pass an array of the two resources to detectConflicts
                                                detectorConflicts = await detector.detectConflicts([resource1, resource2]);
                                            } else {
                                                this.logger.error(`Detector ${detector.constructor.name} does not have a detectConflict or detectConflicts method`);
                                            }
                                        } catch (error: any) {
                                            this.logger.error(`Error calling detectConflicts on detector ${detector.constructor.name}: ${error.message || error}`);
                                        }
                                    }

                                    if (detectorConflicts.length > 0) {
                                        batchConflicts.push(...detectorConflicts);
                                        this.logger.debug(`Detector ${detector.constructor.name} found ${detectorConflicts.length} conflicts between resources ${resource1.id} and ${resource2.id}`);

                                        // Store conflicts in database when batch size is reached
                                        if (batchConflicts.length >= conflictBatchSize) {
                                            try {
                                                const savedCount = this.conflictRepository.saveConflicts(batchConflicts);
                                                totalConflicts += savedCount;
                                                this.logger.debug(`Stored ${savedCount} conflicts in database`);
                                                batchConflicts = []; // Clear batch after storing
                                            } catch (storageError: any) {
                                                this.logger.error(`Error storing conflicts in database: ${storageError.message || storageError}`);
                                            }
                                        }
                                    }

                                    // Untrack this comparison
                                    memoryManager.untrackResource('comparison', 1);
                                } catch (error: any) {
                                    this.logger.error(`Error comparing resources ${resource1.id} and ${resource2.id}: ${error.message || error}`);
                                }
                            }

                            // Check memory pressure after processing each resource
                            const resourceMemoryPressure = memoryManager.getMemoryPressure();
                            const memoryUsage = process.memoryUsage();
                            const isConstrainedHeap = memoryUsage.heapTotal < 500 * 1024 * 1024; // 500MB
                            const pressureThreshold = isConstrainedHeap ? 0.98 : 0.8; // 98% for constrained, 80% for normal

                            if (resourceMemoryPressure > pressureThreshold) {
                                this.logger.warn(`High memory pressure detected (${(resourceMemoryPressure * 100).toFixed(1)}%), forcing garbage collection...`);

                                // Force garbage collection
                                memoryManager.forceGarbageCollection();

                                // Log memory usage after garbage collection
                                this.logger.info(`Memory usage after forced GC: ${this.formatMemoryUsage()}`);

                                // Add a small delay to allow memory to be reclaimed
                                await new Promise(resolve => setTimeout(resolve, 100));
                            }
                        }

                        // Store any remaining conflicts in the batch
                        if (batchConflicts.length > 0) {
                            try {
                                const savedCount = this.conflictRepository.saveConflicts(batchConflicts);
                                totalConflicts += savedCount;
                                this.logger.debug(`Stored ${savedCount} remaining conflicts in database`);
                                batchConflicts = []; // Clear batch after storing
                            } catch (storageError: any) {
                                this.logger.error(`Error storing remaining conflicts in database: ${storageError.message || storageError}`);
                            }
                        }

                        const batchEndTime = Date.now();
                        this.logger.debug(`Batch ${batchIndex + 1} processed in ${batchEndTime - batchStartTime}ms`);

                        // Release batch resources
                        await resourceTracker.releaseResource(batchId);

                        // Force garbage collection after each batch
                        memoryManager.forceGarbageCollection();

                        // Log memory usage after batch
                        this.logger.info(`Memory usage after batch ${batchIndex + 1}: ${this.formatMemoryUsage()}`);
                    }

                    // Release detector resources
                    await resourceTracker.releaseResource(detectorId);

                    this.logger.debug(`Detector ${detector.constructor.name} completed`);
                } catch (error: any) {
                    this.logger.error(`Error in detector ${detector.constructor.name}: ${error.message || error}`);
                }
            }

            // Retrieve conflicts from database
            let finalConflicts: ConflictInfo[] = [];
            try {
                this.logger.info(`Retrieving conflicts from database (limit: ${this.options.maxConflictsToReturn || 100})...`);
                finalConflicts = this.conflictRepository.getConflicts(this.options.maxConflictsToReturn || 100);
                this.logger.debug(`Retrieved ${finalConflicts.length} conflicts from database`);
            } catch (retrievalError: any) {
                this.logger.error(`Error retrieving conflicts from database: ${retrievalError.message || retrievalError}`);
            }

            // Sort conflicts by severity if enabled
            if (this.options.sortBySeverity) {
                finalConflicts = this.sortConflictsBySeverity(finalConflicts);
                this.logger.debug(`Sorted ${finalConflicts.length} conflicts by severity`);
            }

            // Calculate severity and type breakdowns
            const severityBreakdown = this.calculateSeverityBreakdown(finalConflicts);
            const typeBreakdown = this.calculateTypeBreakdown(finalConflicts);

            const endTime = Date.now();
            const timeTaken = endTime - startTime;

            // Log final memory usage
            this.logger.info(`Memory usage after conflict detection: ${this.formatMemoryUsage()}`);
            this.logger.info(`Detected ${totalConflicts} conflicts in ${timeTaken}ms, returning ${finalConflicts.length}`);

            return {
                conflicts: finalConflicts,
                resourcesCompared: resourcesToCheck.length,
                timeTaken,
                severityBreakdown,
                typeBreakdown
            };
        } catch (error: any) {
            this.logger.error(`Error in conflict detection: ${error.message || error}`);
            if (error.stack) {
                this.logger.error(error.stack);
            }

            return {
                conflicts: [],
                resourcesCompared: 0,
                timeTaken: Date.now() - startTime,
                severityBreakdown: this.calculateSeverityBreakdown([]),
                typeBreakdown: this.calculateTypeBreakdown([])
            };
        } finally {
            // Ensure all resources are released
            try {
                // Release the operation resource
                await resourceTracker.releaseResource(operationResourceId);

                // Untrack the operation
                memoryManager.untrackResource('conflictDetection', 1);

                // Force final garbage collection
                memoryManager.forceGarbageCollection();

                // Log final memory state
                this.logger.info(`Final memory usage: ${this.formatMemoryUsage()}`);
            } catch (cleanupError: any) {
                this.logger.error(`Error during cleanup: ${cleanupError.message || cleanupError}`);
            }
        }
    }

    /**
     * Deduplicate conflicts based on their ID
     * @param conflicts Array of conflicts to deduplicate
     * @returns Deduplicated array of conflicts
     */
    private deduplicateConflicts(conflicts: ConflictInfo[]): ConflictInfo[] {
        const uniqueConflicts = new Map<string, ConflictInfo>();

        for (const conflict of conflicts) {
            uniqueConflicts.set(conflict.id, conflict);
        }

        return Array.from(uniqueConflicts.values());
    }

    /**
     * Sort conflicts by severity (critical first, low last)
     * @param conflicts Array of conflicts to sort
     * @returns Sorted array of conflicts
     */
    private sortConflictsBySeverity(conflicts: ConflictInfo[]): ConflictInfo[] {
        const severityOrder = {
            [ConflictSeverity.CRITICAL]: 0,
            [ConflictSeverity.HIGH]: 1,
            [ConflictSeverity.MEDIUM]: 2,
            [ConflictSeverity.LOW]: 3
        };

        return [...conflicts].sort((a, b) => {
            const severityA = severityOrder[a.severity] || 999;
            const severityB = severityOrder[b.severity] || 999;
            return severityA - severityB;
        });
    }

    /**
     * Calculate breakdown of conflicts by severity
     * @param conflicts Array of conflicts
     * @returns Breakdown of conflicts by severity
     */
    private calculateSeverityBreakdown(conflicts: ConflictInfo[]): { [key in ConflictSeverity]: number } {
        const breakdown = {
            [ConflictSeverity.CRITICAL]: 0,
            [ConflictSeverity.HIGH]: 0,
            [ConflictSeverity.MEDIUM]: 0,
            [ConflictSeverity.LOW]: 0
        };

        for (const conflict of conflicts) {
            breakdown[conflict.severity]++;
        }

        return breakdown;
    }

    /**
     * Calculate breakdown of conflicts by type
     * @param conflicts Array of conflicts
     * @returns Breakdown of conflicts by type
     */
    private calculateTypeBreakdown(conflicts: ConflictInfo[]): { [key in ConflictType]: number } {
        const breakdown = {} as { [key in ConflictType]: number };

        // Initialize all conflict types to 0
        for (const type in ConflictType) {
            breakdown[type as ConflictType] = 0;
        }

        for (const conflict of conflicts) {
            breakdown[conflict.type]++;
        }

        return breakdown;
    }

    /**
     * Calculate the size of a resource for memory tracking
     * @param resource Resource to calculate size for
     * @returns Size in bytes
     */
    private calculateResourceSize(resource: ResourceInfo): number {
        let size = 0;

        // Add size of basic properties
        size += 8; // id (number)
        size += 8; // packageId (number)

        // Add size of key
        if (resource.key) {
            size += 8; // type (number)
            size += 8; // group (bigint)
            size += 8; // instance (bigint)
        }

        // Add size of other properties
        size += 8; // type (number)
        size += 8; // group (number)
        size += 8; // instance (number)
        size += (resource.resourceType?.length || 0) * 2; // resourceType (string)
        size += 8; // size (number)
        size += 8; // offset (number)
        size += 1; // compressed (boolean)
        size += 8; // decompressedSize (number)
        size += 1; // deleted (boolean)

        // Add size of metadata
        if (resource.metadata) {
            size += JSON.stringify(resource.metadata).length * 2;
        }

        // Add size of content snippet
        if (resource.contentSnippet) {
            size += resource.contentSnippet.length * 2;
        }

        // Add size of package path
        if (resource.packagePath) {
            size += resource.packagePath.length * 2;
        }

        return size;
    }

    /**
     * Calculate adaptive batch size based on memory pressure
     * @param memoryPressure Memory pressure (0-1)
     * @returns Batch size
     */
    private calculateAdaptiveBatchSize(memoryPressure: number): number {
        // Base batch size
        const baseBatchSize = 10;

        if (memoryPressure > 0.9) {
            // Extreme memory pressure - use minimum batch size
            return 1;
        } else if (memoryPressure > 0.8) {
            // Very high memory pressure
            return 2;
        } else if (memoryPressure > 0.7) {
            // High memory pressure
            return 5;
        } else if (memoryPressure > 0.5) {
            // Moderate memory pressure
            return baseBatchSize;
        } else if (memoryPressure > 0.3) {
            // Low memory pressure
            return baseBatchSize * 2;
        } else {
            // Very low memory pressure
            return baseBatchSize * 3;
        }
    }

    /**
     * Format memory usage for logging
     * @returns Formatted memory usage string
     */
    private formatMemoryUsage(): string {
        const memoryManager = EnhancedMemoryManager.getInstance();
        const memStats = memoryManager.getMemoryStats();

        return `Heap used ${formatBytes(memStats.heapUsed)} / ${formatBytes(memStats.heapTotal)} (${Math.round(memStats.usedPercentage)}%), RSS: ${formatBytes(memStats.rss)}`;
    }

    /**
     * Dispose of resources used by the conflict detector
     */
    public async dispose(): Promise<void> {
        this.logger.debug('Disposing ConflictDetector resources');

        // Get resource tracker
        const resourceTracker = ResourceTracker.getInstance();

        try {
            // Dispose of each detector if it has a dispose method
            for (const detector of this.detectors) {
                if (typeof (detector as any).dispose === 'function') {
                    try {
                        await (detector as any).dispose();
                        this.logger.debug(`Disposed ${detector.constructor.name}`);
                    } catch (error: any) {
                        this.logger.error(`Error disposing ${detector.constructor.name}: ${error.message || error}`);
                    }
                }
            }

            // Clear detectors array
            this.detectors = [];

            // Dispose of semantic services if they exist
            if (this.semanticServices) {
                try {
                    // Dispose of each semantic service if it has a dispose method
                    for (const [key, service] of Object.entries(this.semanticServices)) {
                        if (typeof (service as any).dispose === 'function') {
                            await (service as any).dispose();
                            this.logger.debug(`Disposed semantic service: ${key}`);
                        }
                    }
                } catch (error: any) {
                    this.logger.error(`Error disposing semantic services: ${error.message || error}`);
                }

                // Clear semantic services
                this.semanticServices = undefined;
            }

            // Dispose of dependency graph services if they exist
            if (this.dependencyGraphServices) {
                try {
                    // Dispose of each dependency graph service if it has a dispose method
                    for (const [key, service] of Object.entries(this.dependencyGraphServices)) {
                        if (typeof (service as any).dispose === 'function') {
                            await (service as any).dispose();
                            this.logger.debug(`Disposed dependency graph service: ${key}`);
                        }
                    }
                } catch (error: any) {
                    this.logger.error(`Error disposing dependency graph services: ${error.message || error}`);
                }

                // Clear dependency graph services
                this.dependencyGraphServices = undefined;
            }

            // Release all resources tracked by this conflict detector
            await resourceTracker.releaseResourcesByOwner('ConflictDetector');

            // Force garbage collection
            const memoryManager = EnhancedMemoryManager.getInstance();
            memoryManager.forceGarbageCollection();

            this.logger.debug('ConflictDetector resources disposed successfully');
        } catch (error: any) {
            this.logger.error(`Error disposing ConflictDetector resources: ${error.message || error}`);
        }
    }
}
