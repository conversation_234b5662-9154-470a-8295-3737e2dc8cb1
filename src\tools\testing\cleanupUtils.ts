/**
 * Cleanup utilities for test scripts
 *
 * This module provides utilities for cleaning up resources after tests.
 */

import { Logger } from '../../utils/logging/logger.js';
import { DatabaseService } from '../../services/databaseService.js';
import { PackageAnalyzer } from '../../services/analysis/packageAnalyzer.js';
import { ResourcePurposeAnalyzer } from '../../services/analysis/semantic/resourcePurposeAnalyzer.js';
import { EnhancedDependencyChainAnalyzer } from '../../services/analysis/semantic/enhancedDependencyChainAnalyzer.js';
import { GameplaySystemRegistry } from '../../services/analysis/semantic/gameplaySystemRegistry.js';
import { TS4ScriptAnalyzer } from '../../services/analysis/ts4script/ts4ScriptAnalyzer.js';
import { GameVersionDetector } from '../../services/versioning/gameVersionDetector.js';
import { SchemaVersionRegistry } from '../../services/versioning/schemaVersionRegistry.js';
import { ResourceTypeRegistry } from '../../services/versioning/ResourceTypeRegistry.js';
import { ResourceMetadataExtractor } from '../../services/analysis/package/resourceMetadataExtractor.js';
import { CrossResourceAnalyzer } from '../../services/analysis/crossResourceAnalyzer.js';
import { ConflictDetector } from '../../services/conflict/ConflictDetector.js';
import TaskDispatcher from '../../utils/parallel/taskDispatcher.js';
import { createPrintFunction } from '../../utils/console/consoleOutput.js';

// Create a print function for direct output
const print = createPrintFunction();

/**
 * Clean up resources after a test
 */
export async function cleanupResources(
    packageAnalyzer: PackageAnalyzer | null,
    resourcePurposeAnalyzer: ResourcePurposeAnalyzer | null,
    dependencyChainAnalyzer: EnhancedDependencyChainAnalyzer | null,
    gameplaySystemRegistry: GameplaySystemRegistry | null,
    ts4ScriptAnalyzer: TS4ScriptAnalyzer | null,
    gameVersionDetector: GameVersionDetector | null,
    schemaVersionRegistry: SchemaVersionRegistry | null,
    resourceTypeRegistry: ResourceTypeRegistry | null,
    resourceMetadataExtractor: ResourceMetadataExtractor | null,
    crossResourceAnalyzer: CrossResourceAnalyzer | null,
    conflictDetector: ConflictDetector | null,
    taskDispatcher: TaskDispatcher | null,
    databaseService: DatabaseService | null
): Promise<void> {
    try {
        // Dispose of the package analyzer if it exists
        if (packageAnalyzer) {
            print('Disposing package analyzer...');
            await packageAnalyzer.dispose();
            print('Package analyzer disposed.');
        }

        // Dispose of the resource purpose analyzer if it exists
        if (resourcePurposeAnalyzer) {
            print('Disposing resource purpose analyzer...');
            await resourcePurposeAnalyzer.dispose();
            print('Resource purpose analyzer disposed.');
        }

        // Dispose of the dependency chain analyzer if it exists
        if (dependencyChainAnalyzer) {
            print('Disposing dependency chain analyzer...');
            await dependencyChainAnalyzer.dispose();
            print('Dependency chain analyzer disposed.');
        }

        // Dispose of the gameplay system registry if it exists
        if (gameplaySystemRegistry) {
            print('Disposing gameplay system registry...');
            await gameplaySystemRegistry.dispose();
            print('Gameplay system registry disposed.');
        }

        // TS4Script analyzer doesn't need disposal
        if (ts4ScriptAnalyzer) {
            print('TS4Script analyzer cleanup not needed (no dispose method).');
        }

        // Dispose of the game version detector if it exists
        if (gameVersionDetector) {
            print('Disposing game version detector...');
            await gameVersionDetector.dispose();
            print('Game version detector disposed.');
        }

        // Dispose of the schema version registry if it exists
        if (schemaVersionRegistry) {
            print('Disposing schema version registry...');
            await schemaVersionRegistry.dispose();
            print('Schema version registry disposed.');
        }

        // Dispose of the resource type registry if it exists
        if (resourceTypeRegistry) {
            print('Disposing resource type registry...');
            await resourceTypeRegistry.dispose();
            print('Resource type registry disposed.');
        }

        // Resource metadata extractor doesn't need disposal
        if (resourceMetadataExtractor) {
            print('Resource metadata extractor cleanup not needed (no dispose method).');
        }

        // Dispose of the cross-resource analyzer if it exists
        if (crossResourceAnalyzer) {
            print('Disposing cross-resource analyzer...');
            await crossResourceAnalyzer.dispose();
            print('Cross-resource analyzer disposed.');
        }

        // Dispose of the conflict detector if it exists
        if (conflictDetector) {
            print('Disposing conflict detector...');
            await conflictDetector.dispose();
            print('Conflict detector disposed.');
        }

        // Terminate the task dispatcher if it exists
        if (taskDispatcher) {
            print('Terminating task dispatcher...');
            await taskDispatcher.terminate();
            print('Task dispatcher terminated.');
        }

        // Always close the database connection
        if (databaseService) {
            print('Closing database connection...');
            databaseService.close();
            print('Database connection closed.');
        }
    } catch (cleanupError: any) {
        print(`Error during cleanup: ${cleanupError.message || cleanupError}`);
    }
}
