/**
 * Data Validator
 * 
 * This module provides specialized validation for Sims 4 data structures.
 * It helps ensure that data is valid and provides consistent error handling.
 * 
 * Features:
 * - Package validation
 * - Resource validation
 * - SimData validation
 * - Tuning validation
 * - Integrity validation
 * - Consistency validation
 * - Relationship validation
 */

import { Logger } from '../logging/logger.js';
import { AppError, ErrorCategory, ErrorCode, ErrorSeverity } from '../error/errorTypes.js';
import { EnhancedErrorHandler } from '../error/enhancedErrorHandler.js';
import { Validator, ValidationLevel, ValidationResult, ValidationOptions } from './validator.js';

// Create a logger for this module
const logger = new Logger('DataValidator');

// Get error handler instance
const errorHandler = EnhancedErrorHandler.getInstance();

// Get validator instance
const validator = Validator.getInstance();

/**
 * Data integrity check result
 */
export interface IntegrityCheckResult {
    valid: boolean;
    level: ValidationLevel;
    message: string;
    code: ErrorCode;
    details?: {
        expectedChecksum?: string;
        actualChecksum?: string;
        expectedSize?: number;
        actualSize?: number;
        expectedVersion?: number;
        actualVersion?: number;
        corruptedSections?: string[];
        repairAttempted?: boolean;
        repairSuccessful?: boolean;
    };
}

/**
 * Data validator class
 */
export class DataValidator {
    private static instance: DataValidator;
    
    /**
     * Create a new data validator
     */
    private constructor() {
        logger.info('Data validator initialized');
    }
    
    /**
     * Get the data validator instance
     * @returns Data validator instance
     */
    public static getInstance(): DataValidator {
        if (!DataValidator.instance) {
            DataValidator.instance = new DataValidator();
        }
        
        return DataValidator.instance;
    }
    
    /**
     * Validate package header
     * @param header Package header
     * @param options Validation options
     * @returns Validation result
     */
    public validatePackageHeader(
        header: any,
        options: ValidationOptions = {}
    ): ValidationResult {
        // Validate header exists
        const headerResult = validator.validateRequired(header, 'Package header', options);
        if (!headerResult.valid) {
            return headerResult;
        }
        
        // Validate header type
        const typeResult = validator.validateType(header, 'object', 'Package header', options);
        if (!typeResult.valid) {
            return typeResult;
        }
        
        // Validate header magic
        if (header.magic !== 'DBPF') {
            return {
                valid: false,
                level: options.level || ValidationLevel.ERROR,
                message: `Invalid package header magic: ${header.magic}`,
                code: ErrorCode.INVALID_FORMAT,
                details: {
                    expectedMagic: 'DBPF',
                    actualMagic: header.magic
                }
            };
        }
        
        // Validate header version
        if (header.majorVersion !== 2 || header.minorVersion !== 0) {
            return {
                valid: false,
                level: options.level || ValidationLevel.WARNING,
                message: `Unsupported package version: ${header.majorVersion}.${header.minorVersion}`,
                code: ErrorCode.INVALID_VERSION,
                details: {
                    expectedVersion: '2.0',
                    actualVersion: `${header.majorVersion}.${header.minorVersion}`
                }
            };
        }
        
        return {
            valid: true,
            level: ValidationLevel.INFO,
            message: 'Package header is valid',
            code: ErrorCode.NONE
        };
    }
    
    /**
     * Validate resource entry
     * @param entry Resource entry
     * @param options Validation options
     * @returns Validation result
     */
    public validateResourceEntry(
        entry: any,
        options: ValidationOptions = {}
    ): ValidationResult {
        // Validate entry exists
        const entryResult = validator.validateRequired(entry, 'Resource entry', options);
        if (!entryResult.valid) {
            return entryResult;
        }
        
        // Validate entry type
        const typeResult = validator.validateType(entry, 'object', 'Resource entry', options);
        if (!typeResult.valid) {
            return typeResult;
        }
        
        // Validate required fields
        const requiredFields = ['type', 'group', 'instance', 'offset', 'fileSize'];
        
        for (const field of requiredFields) {
            if (entry[field] === undefined) {
                return {
                    valid: false,
                    level: options.level || ValidationLevel.ERROR,
                    message: `Resource entry is missing required field: ${field}`,
                    code: ErrorCode.MISSING_REQUIRED_FIELD,
                    details: {
                        missingField: field
                    }
                };
            }
        }
        
        // Validate field types
        if (typeof entry.type !== 'number') {
            return {
                valid: false,
                level: options.level || ValidationLevel.ERROR,
                message: 'Resource type must be a number',
                code: ErrorCode.INVALID_TYPE,
                details: {
                    expectedType: 'number',
                    actualType: typeof entry.type
                }
            };
        }
        
        if (typeof entry.offset !== 'number' || entry.offset < 0) {
            return {
                valid: false,
                level: options.level || ValidationLevel.ERROR,
                message: 'Resource offset must be a non-negative number',
                code: ErrorCode.INVALID_VALUE,
                details: {
                    expectedType: 'non-negative number',
                    actualValue: entry.offset
                }
            };
        }
        
        if (typeof entry.fileSize !== 'number' || entry.fileSize < 0) {
            return {
                valid: false,
                level: options.level || ValidationLevel.ERROR,
                message: 'Resource fileSize must be a non-negative number',
                code: ErrorCode.INVALID_VALUE,
                details: {
                    expectedType: 'non-negative number',
                    actualValue: entry.fileSize
                }
            };
        }
        
        return {
            valid: true,
            level: ValidationLevel.INFO,
            message: 'Resource entry is valid',
            code: ErrorCode.NONE
        };
    }
    
    /**
     * Validate SimData header
     * @param header SimData header
     * @param options Validation options
     * @returns Validation result
     */
    public validateSimDataHeader(
        header: any,
        options: ValidationOptions = {}
    ): ValidationResult {
        // Validate header exists
        const headerResult = validator.validateRequired(header, 'SimData header', options);
        if (!headerResult.valid) {
            return headerResult;
        }
        
        // Validate header type
        const typeResult = validator.validateType(header, 'object', 'SimData header', options);
        if (!typeResult.valid) {
            return typeResult;
        }
        
        // Validate required fields
        const requiredFields = ['version', 'flags'];
        
        for (const field of requiredFields) {
            if (header[field] === undefined) {
                return {
                    valid: false,
                    level: options.level || ValidationLevel.ERROR,
                    message: `SimData header is missing required field: ${field}`,
                    code: ErrorCode.MISSING_REQUIRED_FIELD,
                    details: {
                        missingField: field
                    }
                };
            }
        }
        
        // Validate version
        if (typeof header.version !== 'number') {
            return {
                valid: false,
                level: options.level || ValidationLevel.ERROR,
                message: 'SimData version must be a number',
                code: ErrorCode.INVALID_TYPE,
                details: {
                    expectedType: 'number',
                    actualType: typeof header.version
                }
            };
        }
        
        // Check for supported versions
        const supportedVersions = [1, 2, 3, 4, 5];
        if (!supportedVersions.includes(header.version)) {
            return {
                valid: false,
                level: options.level || ValidationLevel.WARNING,
                message: `Unsupported SimData version: ${header.version}`,
                code: ErrorCode.INVALID_VERSION,
                details: {
                    supportedVersions,
                    actualVersion: header.version
                }
            };
        }
        
        return {
            valid: true,
            level: ValidationLevel.INFO,
            message: 'SimData header is valid',
            code: ErrorCode.NONE
        };
    }
    
    /**
     * Check data integrity
     * @param data Data to check
     * @param expectedChecksum Expected checksum
     * @param options Validation options
     * @returns Integrity check result
     */
    public checkIntegrity(
        data: Buffer,
        expectedChecksum: string,
        options: ValidationOptions = {}
    ): IntegrityCheckResult {
        // Validate data exists
        const dataResult = validator.validateRequired(data, 'Data', options);
        if (!dataResult.valid) {
            return {
                valid: false,
                level: options.level || ValidationLevel.ERROR,
                message: 'Data is required for integrity check',
                code: ErrorCode.MISSING_REQUIRED_FIELD
            };
        }
        
        // Validate data type
        if (!Buffer.isBuffer(data)) {
            return {
                valid: false,
                level: options.level || ValidationLevel.ERROR,
                message: 'Data must be a buffer',
                code: ErrorCode.INVALID_TYPE,
                details: {
                    expectedType: 'Buffer',
                    actualType: typeof data
                }
            };
        }
        
        // Calculate actual checksum
        const crypto = require('crypto');
        const hash = crypto.createHash('sha256');
        hash.update(data);
        const actualChecksum = hash.digest('hex');
        
        // Compare checksums
        const valid = actualChecksum === expectedChecksum;
        
        return {
            valid,
            level: valid ? ValidationLevel.INFO : ValidationLevel.ERROR,
            message: valid ? 'Data integrity check passed' : 'Data integrity check failed',
            code: valid ? ErrorCode.NONE : ErrorCode.RESOURCE_CORRUPTED,
            details: {
                expectedChecksum,
                actualChecksum
            }
        };
    }
    
    /**
     * Detect data corruption
     * @param data Data to check
     * @param resourceType Resource type
     * @param options Validation options
     * @returns Integrity check result
     */
    public detectCorruption(
        data: Buffer,
        resourceType: number,
        options: ValidationOptions = {}
    ): IntegrityCheckResult {
        // Validate data exists
        const dataResult = validator.validateRequired(data, 'Data', options);
        if (!dataResult.valid) {
            return {
                valid: false,
                level: options.level || ValidationLevel.ERROR,
                message: 'Data is required for corruption detection',
                code: ErrorCode.MISSING_REQUIRED_FIELD
            };
        }
        
        // Validate data type
        if (!Buffer.isBuffer(data)) {
            return {
                valid: false,
                level: options.level || ValidationLevel.ERROR,
                message: 'Data must be a buffer',
                code: ErrorCode.INVALID_TYPE,
                details: {
                    expectedType: 'Buffer',
                    actualType: typeof data
                }
            };
        }
        
        // Check for corruption based on resource type
        const corruptedSections: string[] = [];
        
        switch (resourceType) {
            case 0x545AC67A: // SimData
                // Check SimData header
                if (data.length < 8) {
                    corruptedSections.push('header');
                } else {
                    const version = data.readUInt16LE(0);
                    const flags = data.readUInt16LE(2);
                    
                    // Check for valid version
                    if (version < 1 || version > 5) {
                        corruptedSections.push('version');
                    }
                    
                    // Check for instance count
                    if (data.length >= 8) {
                        const instanceCount = data.readUInt32LE(4);
                        
                        // Check for reasonable instance count
                        if (instanceCount > 10000) {
                            corruptedSections.push('instanceCount');
                        }
                    }
                }
                break;
                
            case 0x0166038C: // Tuning XML
                // Check for XML header
                if (data.length < 5 || data.toString('utf8', 0, 5) !== '<?xml') {
                    corruptedSections.push('xmlHeader');
                }
                
                // Check for XML closing tag
                if (data.length < 10 || !data.toString('utf8').includes('</SimData>')) {
                    corruptedSections.push('xmlClosingTag');
                }
                break;
                
            case 0x00B2D882: // CASP
                // Check CASP header
                if (data.length < 4 || data.readUInt32LE(0) !== 0x00000001) {
                    corruptedSections.push('caspHeader');
                }
                break;
                
            default:
                // Generic corruption check
                // Check for zero-length data
                if (data.length === 0) {
                    corruptedSections.push('emptyData');
                }
                
                // Check for all zeros
                let allZeros = true;
                for (let i = 0; i < Math.min(data.length, 100); i++) {
                    if (data[i] !== 0) {
                        allZeros = false;
                        break;
                    }
                }
                
                if (allZeros && data.length > 0) {
                    corruptedSections.push('allZeros');
                }
                break;
        }
        
        const valid = corruptedSections.length === 0;
        
        return {
            valid,
            level: valid ? ValidationLevel.INFO : ValidationLevel.ERROR,
            message: valid ? 'No corruption detected' : `Corruption detected in sections: ${corruptedSections.join(', ')}`,
            code: valid ? ErrorCode.NONE : ErrorCode.RESOURCE_CORRUPTED,
            details: {
                corruptedSections
            }
        };
    }
}
