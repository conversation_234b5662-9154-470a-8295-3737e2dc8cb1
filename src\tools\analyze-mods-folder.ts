#!/usr/bin/env node
/**
 * Full Mods Folder Analysis Tool for Sims 4 Mod Management
 * 
 * Comprehensive analysis of entire mod collections with conflict detection
 * between ALL packages in a user's chosen directory.
 * 
 * Usage: npx tsx src/tools/analyze-mods-folder.ts --folder "path/to/mods" [options]
 */

import { createPrintFunction } from '../utils/console/consoleOutput.js';
import { EventEmitter } from 'events';

const print = createPrintFunction();

interface FullAnalysisOptions {
    /** Path to mods folder */
    folder: string;
    /** Batch size for processing (default: 50) */
    batchSize?: number;
    /** Maximum packages to analyze (default: unlimited) */
    maxPackages?: number;
    /** Clear database before analysis */
    clearDatabase?: boolean;
    /** Enable conflict detection */
    enableConflictDetection?: boolean;
    /** Output format: console, json, csv, all */
    outputFormat?: 'console' | 'json' | 'csv' | 'all';
    /** Output file path (for json/csv) */
    outputPath?: string;
    /** Log level */
    logLevel?: 'error' | 'warn' | 'info' | 'debug';
    /** Include TS4Script analysis */
    includeTS4Scripts?: boolean;
    /** Memory limit in MB */
    memoryLimit?: number;
}

interface FullAnalysisResult {
    /** Folder scan results */
    scanResult: {
        totalFiles: number;
        totalSize: number;
        scanDuration: number;
        extensionBreakdown: Record<string, { count: number; size: number }>;
    };
    /** Package analysis results */
    analysisResult: {
        packagesAnalyzed: number;
        packagesFailed: number;
        analysisDuration: number;
        resourcesExtracted: number;
    };
    /** Conflict detection results */
    conflictResult?: {
        conflictsDetected: number;
        conflictsByType: Record<string, number>;
        conflictsBySeverity: Record<string, number>;
        detectionDuration: number;
    };
    /** Performance metrics */
    performanceMetrics: {
        totalDuration: number;
        peakMemoryUsage: number;
        averagePackageProcessingTime: number;
    };
    /** Errors encountered */
    errors: string[];
}

/**
 * Parse command line arguments
 */
function parseArgs(): FullAnalysisOptions {
    const args = process.argv.slice(2);
    const options: FullAnalysisOptions = {
        folder: '',
        batchSize: 50,
        maxPackages: undefined,
        clearDatabase: true,
        enableConflictDetection: true,
        outputFormat: 'console',
        logLevel: 'info',
        includeTS4Scripts: false,
        memoryLimit: 2048 // 2GB default
    };

    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        const nextArg = args[i + 1];

        switch (arg) {
            case '--folder':
            case '-f':
                options.folder = nextArg;
                i++;
                break;
            case '--batch-size':
                options.batchSize = parseInt(nextArg) || 50;
                i++;
                break;
            case '--max-packages':
                options.maxPackages = parseInt(nextArg);
                i++;
                break;
            case '--no-clear-db':
                options.clearDatabase = false;
                break;
            case '--no-conflicts':
                options.enableConflictDetection = false;
                break;
            case '--output-format':
                options.outputFormat = nextArg as any;
                i++;
                break;
            case '--output-path':
                options.outputPath = nextArg;
                i++;
                break;
            case '--log-level':
                options.logLevel = nextArg as any;
                i++;
                break;
            case '--include-ts4scripts':
                options.includeTS4Scripts = true;
                break;
            case '--memory-limit':
                options.memoryLimit = parseInt(nextArg) || 2048;
                i++;
                break;
            case '--help':
            case '-h':
                printHelp();
                process.exit(0);
                break;
        }
    }

    if (!options.folder) {
        print('❌ Error: --folder parameter is required');
        printHelp();
        process.exit(1);
    }

    return options;
}

/**
 * Print help information
 */
function printHelp(): void {
    console.log(`
🎮 Sims 4 Full Mods Folder Analysis Tool

Analyzes ALL mods in a folder and detects conflicts between EVERY package combination.

Usage: npx tsx src/tools/analyze-mods-folder.ts --folder "path/to/mods" [options]

Required:
  --folder, -f <path>          Path to mods folder to analyze

Options:
  --batch-size <number>        Packages per batch (default: 50)
  --max-packages <number>      Maximum packages to analyze (default: unlimited)
  --no-clear-db               Don't clear database before analysis
  --no-conflicts             Skip conflict detection
  --output-format <format>    Output format: console, json, csv, all (default: console)
  --output-path <path>        Output file path (for json/csv)
  --log-level <level>         Log level: error, warn, info, debug (default: info)
  --include-ts4scripts        Include TS4Script analysis
  --memory-limit <mb>         Memory limit in MB (default: 2048)
  --help, -h                  Show this help

Examples:
  # Analyze all mods in default Sims 4 mods folder
  npx tsx src/tools/analyze-mods-folder.ts --folder "C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods"

  # Analyze with custom settings
  npx tsx src/tools/analyze-mods-folder.ts --folder "./mods" --batch-size 25 --max-packages 100 --output-format json

  # Quick analysis without conflicts
  npx tsx src/tools/analyze-mods-folder.ts --folder "./mods" --no-conflicts --log-level error
`);
}

/**
 * Progress tracker for analysis
 */
class AnalysisProgressTracker extends EventEmitter {
    private startTime: number = Date.now();
    private currentPhase: string = '';
    private packagesProcessed: number = 0;
    private totalPackages: number = 0;

    updatePhase(phase: string, packagesProcessed: number = 0, totalPackages: number = 0): void {
        this.currentPhase = phase;
        this.packagesProcessed = packagesProcessed;
        this.totalPackages = totalPackages;
        
        const elapsed = Date.now() - this.startTime;
        const progress = totalPackages > 0 ? (packagesProcessed / totalPackages * 100).toFixed(1) : '0';
        
        print(`📊 ${phase} | Progress: ${packagesProcessed}/${totalPackages} (${progress}%) | Elapsed: ${Math.round(elapsed/1000)}s`);
        
        this.emit('progress', {
            phase,
            packagesProcessed,
            totalPackages,
            progress: parseFloat(progress),
            elapsed
        });
    }

    logMemoryUsage(): void {
        const usage = process.memoryUsage();
        const heapMB = Math.round(usage.heapUsed / 1024 / 1024);
        const totalMB = Math.round(usage.heapTotal / 1024 / 1024);
        print(`💾 Memory: ${heapMB}MB used / ${totalMB}MB total`);
    }
}

/**
 * Main analysis function
 */
async function runFullFolderAnalysis(options: FullAnalysisOptions): Promise<FullAnalysisResult> {
    // Lazy load heavy modules only when actually running analysis
    const { scanModsFolder } = await import('./testing/fileScanner.js');
    const { analyzeMods } = await import('./testing/modAnalyzer.js');
    const { EnhancedConflictOrchestrator } = await import('../services/conflict/EnhancedConflictOrchestrator.js');
    const { DatabaseService } = await import('../services/databaseService.js');
    const EnhancedMemoryManager = (await import('../utils/memory/enhancedMemoryManager.js')).default;
    const { logger } = await import('../utils/logging/logger.js');

    const startTime = Date.now();
    const progressTracker = new AnalysisProgressTracker();
    const errors: string[] = [];

    print(`\n🎮 ===== SIMS 4 FULL MODS FOLDER ANALYSIS =====`);
    print(`📁 Folder: ${options.folder}`);
    print(`⚙️  Batch size: ${options.batchSize}`);
    print(`🔍 Max packages: ${options.maxPackages || 'unlimited'}`);
    print(`🔥 Conflict detection: ${options.enableConflictDetection ? 'enabled' : 'disabled'}`);
    print(`💾 Memory limit: ${options.memoryLimit}MB`);

    try {
        // Initialize memory manager
        const memoryManager = EnhancedMemoryManager.getInstance({
            thresholds: {
                warning: options.memoryLimit! * 0.7,
                critical: options.memoryLimit! * 0.85,
                emergency: options.memoryLimit! * 0.95
            },
            autoGcEnabled: true,
            trackingEnabled: true
        });
        memoryManager.initialize();

        // Initialize database
        const databaseService = new DatabaseService();
        await databaseService.initialize();

        if (options.clearDatabase) {
            progressTracker.updatePhase('Clearing database');
            await databaseService.clearDatabase();
        }

        // Phase 1: Scan folder
        progressTracker.updatePhase('Scanning folder for mod files');
        const scanOptions: ScanOptions = {
            includeExtensions: options.includeTS4Scripts ? ['.package', '.ts4script'] : ['.package'],
            maxFiles: options.maxPackages,
            includeMetadata: true,
            progressCallback: (count, path) => {
                if (count % 100 === 0) {
                    print(`📂 Found ${count} mod files...`);
                }
            }
        };

        const scanResult = await scanModsFolder(options.folder, scanOptions);
        
        if (scanResult.totalFiles === 0) {
            throw new Error(`No mod files found in folder: ${options.folder}`);
        }

        print(`✅ Scan complete: ${scanResult.totalFiles} files found (${formatBytes(scanResult.totalSize)})`);
        progressTracker.logMemoryUsage();

        // Phase 2: Analyze packages
        const packageFiles = scanResult.files.filter(f => f.extension === '.package');
        const totalBatches = Math.ceil(packageFiles.length / options.batchSize!);
        
        progressTracker.updatePhase('Analyzing packages', 0, packageFiles.length);
        
        let packagesAnalyzed = 0;
        let packagesFailed = 0;
        let resourcesExtracted = 0;

        for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            const batchStart = batchIndex * options.batchSize!;
            const batchEnd = Math.min(batchStart + options.batchSize!, packageFiles.length);
            const batchFiles = packageFiles.slice(batchStart, batchEnd);

            progressTracker.updatePhase(
                `Analyzing batch ${batchIndex + 1}/${totalBatches}`,
                packagesAnalyzed,
                packageFiles.length
            );

            try {
                // Process batch using existing modAnalyzer
                await analyzeMods(
                    batchFiles.map(f => f.path).join(','), // Pass file paths
                    batchFiles.length,
                    options.includeTS4Scripts || false,
                    false, // No parallel processing for memory efficiency
                    1, // Single concurrent task
                    options.memoryLimit! * 1024 * 1024, // Convert MB to bytes
                    false, // No content conflict logging
                    options.logLevel || 'info',
                    5 * 1024 * 1024, // Direct buffer threshold
                    50 * 1024 * 1024, // Chunked processing threshold
                    'normal',
                    'streaming'
                );

                packagesAnalyzed += batchFiles.length;
                
                // Memory cleanup after each batch
                await memoryManager.performCleanup();
                
                if (batchIndex % 5 === 0) {
                    progressTracker.logMemoryUsage();
                }

            } catch (error: any) {
                logger.error(`Batch ${batchIndex + 1} failed: ${error.message}`);
                errors.push(`Batch ${batchIndex + 1}: ${error.message}`);
                packagesFailed += batchFiles.length;
            }
        }

        const analysisDuration = Date.now() - startTime;
        progressTracker.updatePhase('Package analysis complete', packagesAnalyzed, packageFiles.length);

        // Phase 3: Conflict detection (if enabled)
        let conflictResult;
        if (options.enableConflictDetection && packagesAnalyzed > 1) {
            progressTracker.updatePhase('Detecting conflicts between all packages');
            
            const conflictStartTime = Date.now();
            try {
                const conflictOrchestrator = new EnhancedConflictOrchestrator(databaseService);
                const conflicts = await conflictOrchestrator.detectConflicts();
                
                const conflictsByType: Record<string, number> = {};
                const conflictsBySeverity: Record<string, number> = {};
                
                conflicts.conflicts.forEach(conflict => {
                    conflictsByType[conflict.type] = (conflictsByType[conflict.type] || 0) + 1;
                    conflictsBySeverity[conflict.severity] = (conflictsBySeverity[conflict.severity] || 0) + 1;
                });

                conflictResult = {
                    conflictsDetected: conflicts.conflicts.length,
                    conflictsByType,
                    conflictsBySeverity,
                    detectionDuration: Date.now() - conflictStartTime
                };

                print(`✅ Conflict detection complete: ${conflicts.conflicts.length} conflicts found`);
            } catch (error: any) {
                logger.error(`Conflict detection failed: ${error.message}`);
                errors.push(`Conflict detection: ${error.message}`);
            }
        }

        // Compile results
        const totalDuration = Date.now() - startTime;
        const result: FullAnalysisResult = {
            scanResult: {
                totalFiles: scanResult.totalFiles,
                totalSize: scanResult.totalSize,
                scanDuration: scanResult.scanDuration,
                extensionBreakdown: Object.fromEntries(scanResult.extensionStats)
            },
            analysisResult: {
                packagesAnalyzed,
                packagesFailed,
                analysisDuration,
                resourcesExtracted
            },
            conflictResult,
            performanceMetrics: {
                totalDuration,
                peakMemoryUsage: Math.max(...Array.from({length: 10}, () => process.memoryUsage().heapUsed)),
                averagePackageProcessingTime: packagesAnalyzed > 0 ? analysisDuration / packagesAnalyzed : 0
            },
            errors
        };

        return result;

    } catch (error: any) {
        logger.error(`Full folder analysis failed: ${error.message}`);
        throw error;
    }
}

/**
 * Format bytes to human readable string
 */
function formatBytes(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    return `${size.toFixed(1)} ${units[unitIndex]}`;
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
    // Check for help first before loading heavy modules
    const args = process.argv.slice(2);
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
🎮 Sims 4 Full Mods Folder Analysis Tool

Analyzes ALL mods in a folder and detects conflicts between EVERY package combination.

Usage: npx tsx src/tools/analyze-mods-folder.ts --folder "path/to/mods" [options]

Required:
  --folder, -f <path>          Path to mods folder to analyze

Options:
  --batch-size <number>        Packages per batch (default: 50)
  --max-packages <number>      Maximum packages to analyze (default: unlimited)
  --no-clear-db               Don't clear database before analysis
  --no-conflicts             Skip conflict detection
  --output-format <format>    Output format: console, json, csv, all (default: console)
  --output-path <path>        Output file path (for json/csv)
  --log-level <level>         Log level: error, warn, info, debug (default: info)
  --include-ts4scripts        Include TS4Script analysis
  --memory-limit <mb>         Memory limit in MB (default: 2048)
  --help, -h                  Show this help

Examples:
  # Analyze all mods in default Sims 4 mods folder
  npx tsx src/tools/analyze-mods-folder.ts --folder "C:\\\\Users\\\\<USER>\\\\Documents\\\\Electronic Arts\\\\The Sims 4\\\\Mods"

  # Analyze with custom settings
  npx tsx src/tools/analyze-mods-folder.ts --folder "./mods" --batch-size 25 --max-packages 100 --output-format json

  # Quick analysis without conflicts
  npx tsx src/tools/analyze-mods-folder.ts --folder "./mods" --no-conflicts --log-level error
`);
        process.exit(0);
    }

    const options = parseArgs();

    runFullFolderAnalysis(options)
        .then(result => {
            print(`\n🎯 ===== ANALYSIS COMPLETE =====`);
            print(`📊 Files scanned: ${result.scanResult.totalFiles}`);
            print(`📦 Packages analyzed: ${result.analysisResult.packagesAnalyzed}`);
            print(`❌ Packages failed: ${result.analysisResult.packagesFailed}`);
            if (result.conflictResult) {
                print(`⚔️  Conflicts detected: ${result.conflictResult.conflictsDetected}`);
            }
            print(`⏱️  Total duration: ${Math.round(result.performanceMetrics.totalDuration / 1000)}s`);
            print(`💾 Peak memory: ${Math.round(result.performanceMetrics.peakMemoryUsage / 1024 / 1024)}MB`);
            
            if (result.errors.length > 0) {
                print(`\n⚠️  Errors encountered:`);
                result.errors.forEach(error => print(`  - ${error}`));
            }
            
            process.exit(0);
        })
        .catch(error => {
            print(`❌ Analysis failed: ${error.message}`);
            process.exit(1);
        });
}
