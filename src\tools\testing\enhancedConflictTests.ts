import { EnhancedConflictOrchestrator, EnhancedConflictDetectionConfig } from '../../services/conflict/EnhancedConflictOrchestrator.js';
import { DatabaseService } from '../../services/database/DatabaseService.js';
import { ResourceInfo } from '../../types/resource/interfaces.js';
import { ConflictSeverity } from '../../types/conflict/ConflictTypes.js';
import { Logger } from 'winston';

/**
 * Test the enhanced conflict detection system with false positive reduction
 */
export async function testEnhancedConflictDetection(
    resources: ResourceInfo[],
    databaseService: DatabaseService,
    logger: Logger,
    printFunction: (message: string) => void
): Promise<{
    success: boolean;
    originalConflicts: number;
    filteredConflicts: number;
    reductionPercentage: number;
    duration: number;
    severityBreakdown: Record<string, number>;
    typeBreakdown: Record<string, number>;
    error?: string;
}> {
    const startTime = Date.now();
    printFunction('🔍 Testing Enhanced Conflict Detection with False Positive Reduction...');

    try {
        // Test 1: Baseline detection (without filtering)
        printFunction('📊 Phase 1: Baseline conflict detection (no filtering)...');
        const baselineConfig: EnhancedConflictDetectionConfig = {
            enableTGIDetection: true,
            enableCRC32Detection: true,
            enableSims4Filtering: false, // Disabled for baseline
            crc32BufferSize: 2048,
            maxConflictsToReturn: 50000, // No limit for baseline
            enableDetailedLogging: false
        };

        const baselineOrchestrator = new EnhancedConflictOrchestrator(
            databaseService,
            baselineConfig,
            logger
        );

        const baselineConflicts = await baselineOrchestrator.detectConflicts(resources);
        printFunction(`   📈 Baseline conflicts detected: ${baselineConflicts.length}`);

        // Test 2: Enhanced detection (with filtering)
        printFunction('🎯 Phase 2: Enhanced conflict detection (with filtering)...');
        const enhancedConfig: EnhancedConflictDetectionConfig = {
            enableTGIDetection: true,
            enableCRC32Detection: true,
            enableSims4Filtering: true, // Enabled for enhanced
            crc32BufferSize: 2048,
            maxConflictsToReturn: 1000, // Reasonable limit
            enableDetailedLogging: true
        };

        const enhancedOrchestrator = new EnhancedConflictOrchestrator(
            databaseService,
            enhancedConfig,
            logger
        );

        const enhancedConflicts = await enhancedOrchestrator.detectConflicts(resources);
        printFunction(`   🎯 Enhanced conflicts detected: ${enhancedConflicts.length}`);

        // Calculate reduction
        const reductionCount = baselineConflicts.length - enhancedConflicts.length;
        const reductionPercentage = baselineConflicts.length > 0
            ? (reductionCount / baselineConflicts.length) * 100
            : 0;

        printFunction(`   📉 False positives reduced: ${reductionCount} (${reductionPercentage.toFixed(1)}%)`);

        // Analyze severity breakdown
        const severityBreakdown: Record<string, number> = {};
        const typeBreakdown: Record<string, number> = {};

        for (const conflict of enhancedConflicts) {
            severityBreakdown[conflict.severity] = (severityBreakdown[conflict.severity] || 0) + 1;
            typeBreakdown[conflict.type] = (typeBreakdown[conflict.type] || 0) + 1;
        }

        printFunction('📊 Enhanced conflicts breakdown:');
        printFunction('   Severity distribution:');
        for (const [severity, count] of Object.entries(severityBreakdown)) {
            printFunction(`     ${severity}: ${count}`);
        }

        printFunction('   Type distribution:');
        for (const [type, count] of Object.entries(typeBreakdown)) {
            printFunction(`     ${type}: ${count}`);
        }

        // Validate results
        const hasReasonableReduction = reductionPercentage >= 50; // Expect at least 50% reduction
        const hasManageableCount = enhancedConflicts.length <= 1000; // Should be manageable
        const hasCriticalConflicts = enhancedConflicts.some(c =>
            c.severity === ConflictSeverity.CRITICAL || c.severity === ConflictSeverity.HIGH
        );

        const success = hasReasonableReduction && hasManageableCount;

        if (success) {
            printFunction('✅ Enhanced conflict detection test PASSED');
            printFunction(`   ✅ Achieved ${reductionPercentage.toFixed(1)}% false positive reduction`);
            printFunction(`   ✅ Manageable conflict count: ${enhancedConflicts.length}`);
            if (hasCriticalConflicts) {
                printFunction('   ✅ Critical conflicts preserved for user attention');
            }
        } else {
            printFunction('❌ Enhanced conflict detection test FAILED');
            if (!hasReasonableReduction) {
                printFunction(`   ❌ Insufficient false positive reduction: ${reductionPercentage.toFixed(1)}% (expected ≥50%)`);
            }
            if (!hasManageableCount) {
                printFunction(`   ❌ Too many conflicts remain: ${enhancedConflicts.length} (expected ≤1000)`);
            }
        }

        const duration = Date.now() - startTime;

        return {
            success,
            originalConflicts: baselineConflicts.length,
            filteredConflicts: enhancedConflicts.length,
            reductionPercentage,
            duration,
            severityBreakdown,
            typeBreakdown
        };

    } catch (error: any) {
        const duration = Date.now() - startTime;
        printFunction(`❌ Enhanced conflict detection test ERROR: ${error.message}`);

        return {
            success: false,
            originalConflicts: 0,
            filteredConflicts: 0,
            reductionPercentage: 0,
            duration,
            severityBreakdown: {},
            typeBreakdown: {},
            error: error.message
        };
    }
}

/**
 * Test specific Sims 4 conflict scenarios to validate filtering accuracy
 */
export async function testSims4ConflictScenarios(
    databaseService: DatabaseService,
    logger: Logger,
    printFunction: (message: string) => void
): Promise<{
    success: boolean;
    scenarioResults: Array<{
        name: string;
        passed: boolean;
        details: string;
    }>;
    duration: number;
}> {
    const startTime = Date.now();
    printFunction('🎮 Testing Sims 4-specific conflict scenarios...');

    const scenarioResults: Array<{ name: string; passed: boolean; details: string }> = [];

    try {
        const orchestrator = new EnhancedConflictOrchestrator(
            databaseService,
            {
                enableTGIDetection: true,
                enableCRC32Detection: true,
                enableSims4Filtering: true,
                crc32BufferSize: 2048,
                maxConflictsToReturn: 100,
                enableDetailedLogging: true
            },
            logger
        );

        // Scenario 1: Recolor-Mesh relationship (should be filtered out)
        printFunction('📝 Scenario 1: Recolor-Mesh relationship...');
        const recolorMeshResources = createRecolorMeshTestResources();
        const recolorConflicts = await orchestrator.detectConflicts(recolorMeshResources);

        const recolorPassed = recolorConflicts.length === 0;
        scenarioResults.push({
            name: 'Recolor-Mesh Filtering',
            passed: recolorPassed,
            details: recolorPassed
                ? 'Correctly filtered out recolor-mesh relationships'
                : `Incorrectly flagged ${recolorConflicts.length} recolor-mesh conflicts`
        });

        // Scenario 2: Exact TGI match (should NOT be filtered out)
        printFunction('📝 Scenario 2: Exact TGI match...');
        const exactTGIResources = createExactTGITestResources();
        const exactTGIConflicts = await orchestrator.detectConflicts(exactTGIResources);

        const exactTGIPassed = exactTGIConflicts.length > 0;
        scenarioResults.push({
            name: 'Exact TGI Conflict Detection',
            passed: exactTGIPassed,
            details: exactTGIPassed
                ? `Correctly detected ${exactTGIConflicts.length} exact TGI conflicts`
                : 'Failed to detect exact TGI conflicts'
        });

        // Scenario 3: Harmless resource types (should be filtered out)
        printFunction('📝 Scenario 3: Harmless resource types...');
        const harmlessResources = createHarmlessResourceTestResources();
        const harmlessConflicts = await orchestrator.detectConflicts(harmlessResources);

        const harmlessPassed = harmlessConflicts.length === 0;
        scenarioResults.push({
            name: 'Harmless Resource Filtering',
            passed: harmlessPassed,
            details: harmlessPassed
                ? 'Correctly filtered out harmless resource types'
                : `Incorrectly flagged ${harmlessConflicts.length} harmless resource conflicts`
        });

        const allPassed = scenarioResults.every(r => r.passed);
        const duration = Date.now() - startTime;

        if (allPassed) {
            printFunction('✅ All Sims 4 conflict scenarios PASSED');
        } else {
            printFunction('❌ Some Sims 4 conflict scenarios FAILED');
        }

        return {
            success: allPassed,
            scenarioResults,
            duration
        };

    } catch (error: any) {
        const duration = Date.now() - startTime;
        printFunction(`❌ Sims 4 conflict scenarios test ERROR: ${error.message}`);

        return {
            success: false,
            scenarioResults,
            duration
        };
    }
}

// Helper functions to create test resources
function createRecolorMeshTestResources(): ResourceInfo[] {
    return [
        {
            id: 'recolor_test_1',
            type: 0x034AEECB, // CAS_PART
            group: 0x12345678,
            instance: 0x11111111,
            packagePath: 'test_recolor.package',
            resourceType: 'CAS_PART'
        },
        {
            id: 'mesh_test_1',
            type: 0x034AEECB, // CAS_PART
            group: 0x12345678,
            instance: 0x22222222, // Different instance
            packagePath: 'test_mesh.package',
            resourceType: 'CAS_PART'
        }
    ];
}

function createExactTGITestResources(): ResourceInfo[] {
    return [
        {
            id: 'exact_tgi_1',
            type: 0x545AC67A, // SIMDATA
            group: 0x87654321,
            instance: 0x99999999,
            packagePath: 'mod1.package',
            resourceType: 'SIMDATA'
        },
        {
            id: 'exact_tgi_2',
            type: 0x545AC67A, // SIMDATA (same TGI)
            group: 0x87654321,
            instance: 0x99999999,
            packagePath: 'mod2.package',
            resourceType: 'SIMDATA'
        }
    ];
}

function createHarmlessResourceTestResources(): ResourceInfo[] {
    return [
        {
            id: 'harmless_1',
            type: 0x2E75C764, // THUMBNAIL_IMAGE
            group: 0x11111111,
            instance: 0x22222222,
            packagePath: 'harmless1.package',
            resourceType: 'THUMBNAIL_IMAGE'
        },
        {
            id: 'harmless_2',
            type: 0x2E75C764, // THUMBNAIL_IMAGE
            group: 0x11111111,
            instance: 0x33333333,
            packagePath: 'harmless2.package',
            resourceType: 'THUMBNAIL_IMAGE'
        }
    ];
}
