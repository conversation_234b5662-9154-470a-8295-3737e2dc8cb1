/**
 * Memory Leak Detection and Analysis
 * 
 * This module provides comprehensive memory leak detection and analysis
 * to identify and fix memory management issues in the Sims 4 mod analysis system.
 */

import { Logger } from '../../utils/logging/logger.js';
import { EnhancedMemoryManager } from '../../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker, ResourceType, ResourceState } from '../../utils/memory/resourceTracker.js';
import { DatabaseService } from '../../services/databaseService.js';
import { createPackageAnalyzer } from '../../services/analysis/packageAnalyzerFactory.js';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Memory snapshot interface
 */
export interface MemorySnapshot {
    timestamp: number;
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
    arrayBuffers: number;
    pressure: number;
    stage: string;
    details?: any;
}

/**
 * Memory leak analysis result
 */
export interface MemoryLeakAnalysis {
    leakDetected: boolean;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    leakRate: number; // MB per operation
    suspectedSources: string[];
    recommendations: string[];
    snapshots: MemorySnapshot[];
    summary: {
        initialMemory: number;
        finalMemory: number;
        peakMemory: number;
        memoryGrowth: number;
        operationsCompleted: number;
    };
}

/**
 * Take a memory snapshot
 */
export function takeMemorySnapshot(stage: string, details?: any): MemorySnapshot {
    const memUsage = process.memoryUsage();
    const heapUsed = memUsage.heapUsed;
    const heapTotal = memUsage.heapTotal;
    const pressure = heapTotal > 0 ? (heapUsed / heapTotal) * 100 : 0;
    
    return {
        timestamp: Date.now(),
        heapUsed: Math.round(heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(heapTotal / 1024 / 1024), // MB
        external: Math.round(memUsage.external / 1024 / 1024), // MB
        rss: Math.round(memUsage.rss / 1024 / 1024), // MB
        arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024), // MB
        pressure: Math.round(pressure * 10) / 10,
        stage,
        details
    };
}

/**
 * Analyze memory snapshots for leaks
 */
export function analyzeMemoryLeaks(snapshots: MemorySnapshot[]): MemoryLeakAnalysis {
    if (snapshots.length < 2) {
        return {
            leakDetected: false,
            severity: 'LOW',
            leakRate: 0,
            suspectedSources: [],
            recommendations: [],
            snapshots,
            summary: {
                initialMemory: 0,
                finalMemory: 0,
                peakMemory: 0,
                memoryGrowth: 0,
                operationsCompleted: 0
            }
        };
    }
    
    const initial = snapshots[0];
    const final = snapshots[snapshots.length - 1];
    const peak = Math.max(...snapshots.map(s => s.heapUsed));
    
    const memoryGrowth = final.heapUsed - initial.heapUsed;
    const operationsCompleted = snapshots.length - 1;
    const leakRate = operationsCompleted > 0 ? memoryGrowth / operationsCompleted : 0;
    
    // Detect leak patterns
    let leakDetected = false;
    let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
    const suspectedSources: string[] = [];
    const recommendations: string[] = [];
    
    // Check for consistent memory growth
    if (memoryGrowth > 100) { // More than 100MB growth
        leakDetected = true;
        severity = 'HIGH';
        suspectedSources.push('Consistent memory growth detected');
    }
    
    // Check for high memory pressure
    const avgPressure = snapshots.reduce((sum, s) => sum + s.pressure, 0) / snapshots.length;
    if (avgPressure > 90) {
        leakDetected = true;
        if (severity === 'LOW') severity = 'MEDIUM';
        suspectedSources.push('High average memory pressure');
    }
    
    // Check for ArrayBuffer leaks
    const arrayBufferGrowth = final.arrayBuffers - initial.arrayBuffers;
    if (arrayBufferGrowth > 50) { // More than 50MB ArrayBuffer growth
        leakDetected = true;
        suspectedSources.push('ArrayBuffer memory leak detected');
        recommendations.push('Check for unreleased ArrayBuffers in resource processing');
    }
    
    // Check for external memory leaks
    const externalGrowth = final.external - initial.external;
    if (externalGrowth > 50) { // More than 50MB external growth
        leakDetected = true;
        suspectedSources.push('External memory leak detected');
        recommendations.push('Check for unreleased native resources');
    }
    
    // Check for rapid growth patterns
    let rapidGrowthDetected = false;
    for (let i = 1; i < snapshots.length; i++) {
        const growth = snapshots[i].heapUsed - snapshots[i-1].heapUsed;
        if (growth > 20) { // More than 20MB growth in single operation
            rapidGrowthDetected = true;
            break;
        }
    }
    
    if (rapidGrowthDetected) {
        leakDetected = true;
        suspectedSources.push('Rapid memory growth in individual operations');
        recommendations.push('Implement more aggressive garbage collection between operations');
    }
    
    // Generate recommendations
    if (leakDetected) {
        recommendations.push('Force garbage collection more frequently');
        recommendations.push('Implement streaming processing for large resources');
        recommendations.push('Add explicit buffer cleanup after each operation');
        recommendations.push('Monitor and limit concurrent operations');
    }
    
    // Determine final severity
    if (memoryGrowth > 500 || avgPressure > 95) {
        severity = 'CRITICAL';
    } else if (memoryGrowth > 200 || avgPressure > 85) {
        severity = 'HIGH';
    } else if (memoryGrowth > 50 || avgPressure > 75) {
        severity = 'MEDIUM';
    }
    
    return {
        leakDetected,
        severity,
        leakRate,
        suspectedSources,
        recommendations,
        snapshots,
        summary: {
            initialMemory: initial.heapUsed,
            finalMemory: final.heapUsed,
            peakMemory: peak,
            memoryGrowth,
            operationsCompleted
        }
    };
}

/**
 * Run memory leak detection test
 */
export async function runMemoryLeakDetection(
    modsPath: string,
    options: { maxPackages?: number; forceGC?: boolean } = {}
): Promise<MemoryLeakAnalysis> {
    const logger = new Logger('MemoryLeakDetection');
    const snapshots: MemorySnapshot[] = [];
    
    logger.info('===== MEMORY LEAK DETECTION TEST =====');
    
    // Initial snapshot
    snapshots.push(takeMemorySnapshot('initial', { stage: 'test_start' }));
    
    try {
        // Initialize memory manager
        const memoryManager = EnhancedMemoryManager.getInstance();
        snapshots.push(takeMemorySnapshot('memory_manager_init'));
        
        // Initialize database
        const databaseService = new DatabaseService(':memory:');
        await databaseService.initialize();
        snapshots.push(takeMemorySnapshot('database_init'));
        
        const testId = Date.now();
        const resourceTracker = ResourceTracker.getInstance();
        resourceTracker.trackResource(
            ResourceType.DATABASE,
            `memoryLeakTest_${testId}`,
            async () => await databaseService.close(),
            { id: `db_leak_${testId}`, state: ResourceState.ACTIVE }
        );
        
        // Initialize package analyzer
        const packageAnalyzer = createPackageAnalyzer(databaseService, 'streaming');
        await packageAnalyzer.initialize();
        snapshots.push(takeMemorySnapshot('analyzer_init'));
        
        // Get test packages
        const packageFiles = fs.readdirSync(modsPath)
            .filter(file => file.endsWith('.package'))
            .slice(0, options.maxPackages || 3)
            .map(file => path.join(modsPath, file));
        
        logger.info(`Testing memory leaks with ${packageFiles.length} packages`);
        
        // Process packages one by one, taking snapshots
        for (let i = 0; i < packageFiles.length; i++) {
            const packageFile = packageFiles[i];
            const fileName = path.basename(packageFile);
            
            logger.info(`Processing package ${i + 1}/${packageFiles.length}: ${fileName}`);
            
            // Snapshot before processing
            snapshots.push(takeMemorySnapshot(`before_package_${i}`, { fileName }));
            
            // Process package
            const result = await packageAnalyzer.analyzePackage(packageFile);
            
            // Snapshot after processing
            snapshots.push(takeMemorySnapshot(`after_package_${i}`, { 
                fileName, 
                success: result.success,
                error: result.error 
            }));
            
            // Force garbage collection if requested
            if (options.forceGC) {
                memoryManager.forceGarbageCollection();
                snapshots.push(takeMemorySnapshot(`after_gc_${i}`, { fileName }));
            }
            
            // Small delay to allow cleanup
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // Final cleanup
        await resourceTracker.releaseResourcesByOwner(`memoryLeakTest_${testId}`);
        snapshots.push(takeMemorySnapshot('cleanup_complete'));
        
        // Force final garbage collection
        memoryManager.forceGarbageCollection();
        await new Promise(resolve => setTimeout(resolve, 500));
        snapshots.push(takeMemorySnapshot('final_gc'));
        
        // Analyze results
        const analysis = analyzeMemoryLeaks(snapshots);
        
        // Log results
        logger.info(`Memory leak analysis complete:`);
        logger.info(`- Leak detected: ${analysis.leakDetected}`);
        logger.info(`- Severity: ${analysis.severity}`);
        logger.info(`- Memory growth: ${analysis.summary.memoryGrowth}MB`);
        logger.info(`- Peak memory: ${analysis.summary.peakMemory}MB`);
        logger.info(`- Leak rate: ${analysis.leakRate.toFixed(2)}MB per operation`);
        
        if (analysis.suspectedSources.length > 0) {
            logger.warn('Suspected leak sources:');
            analysis.suspectedSources.forEach(source => logger.warn(`  - ${source}`));
        }
        
        if (analysis.recommendations.length > 0) {
            logger.info('Recommendations:');
            analysis.recommendations.forEach(rec => logger.info(`  - ${rec}`));
        }
        
        return analysis;
        
    } catch (error: any) {
        logger.error(`Memory leak detection failed: ${error.message}`);
        snapshots.push(takeMemorySnapshot('error', { error: error.message }));
        
        return analyzeMemoryLeaks(snapshots);
    }
}

/**
 * Generate memory leak report
 */
export function generateMemoryLeakReport(analysis: MemoryLeakAnalysis): string {
    const report = [
        '# Memory Leak Detection Report',
        '',
        `**Leak Detected:** ${analysis.leakDetected ? '⚠️ YES' : '✅ NO'}`,
        `**Severity:** ${analysis.severity}`,
        `**Leak Rate:** ${analysis.leakRate.toFixed(2)} MB per operation`,
        '',
        '## Summary',
        `- Initial Memory: ${analysis.summary.initialMemory} MB`,
        `- Final Memory: ${analysis.summary.finalMemory} MB`,
        `- Peak Memory: ${analysis.summary.peakMemory} MB`,
        `- Memory Growth: ${analysis.summary.memoryGrowth} MB`,
        `- Operations Completed: ${analysis.summary.operationsCompleted}`,
        '',
        '## Suspected Sources',
        ...analysis.suspectedSources.map(source => `- ${source}`),
        '',
        '## Recommendations',
        ...analysis.recommendations.map(rec => `- ${rec}`),
        '',
        '## Memory Snapshots',
        '| Stage | Heap Used (MB) | Heap Total (MB) | Pressure (%) | ArrayBuffers (MB) |',
        '|-------|----------------|-----------------|--------------|-------------------|',
        ...analysis.snapshots.map(snapshot => 
            `| ${snapshot.stage} | ${snapshot.heapUsed} | ${snapshot.heapTotal} | ${snapshot.pressure} | ${snapshot.arrayBuffers} |`
        )
    ];
    
    return report.join('\n');
}
