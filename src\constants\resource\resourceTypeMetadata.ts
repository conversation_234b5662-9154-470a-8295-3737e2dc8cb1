﻿// Corrected imports
import { BinaryResourceType, BinaryResourceTypeValue } from '../../types/resource/core.js'; // Import both type and value
import { ResourceCategory } from '../../types/resource/enums.js'; // Import from enums.js
import { ResourceTypeMetadata } from '../../types/resource/metadata.js';

export const RESOURCE_TYPE_METADATA: { [key: number]: ResourceTypeMetadata } = {
  [0]: { // UNKNOWN
    type: 0,
    name: 'Unknown',
    description: 'Unknown resource type',
    category: ResourceCategory.UNKNOWN,
    dependencies: [],
    mimeTypes: [],
    isCompressible: false,
    isEncryptable: false,
    isModifiable: false,
    hasCustomData: false
  },
  [BinaryResourceTypeValue.CasPart]: {
    type: BinaryResourceTypeValue.CasPart,
    name: 'CAS Part',
    description: 'Create-a-Sim part resource',
    category: ResourceCategory.ASSET,
    dependencies: ['.caspart'],
    mimeTypes: ['application/x-sims4-caspart'],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: true,
    customDataSchema: {
      partType: {
        type: 'string',
        description: 'Type of CAS part',
        required: true
      },
      swatchColors: {
        type: 'array',
        description: 'Swatch color values',
        required: false
      }
    }
  },
  [0x01661233]: { // SOUND_EFFECT (not in S4TK enum)
    type: 0x01661233,
    name: 'Sound Effect',
    description: 'Sound effect resource',
    category: ResourceCategory.SOUND,
    dependencies: ['.wav', '.mp3', '.ogg'],
    mimeTypes: ['audio/wav', 'audio/mpeg', 'audio/ogg'],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: true,
    customDataSchema: {
      duration: {
        type: 'number',
        description: 'Duration in seconds',
        required: true
      },
      sampleRate: {
        type: 'number',
        description: 'Sample rate in Hz',
        required: true
      }
    }
  },
  [0x01661234]: { // SOUND_TRACK (not in S4TK enum)
    type: 0x01661234,
    name: 'Sound Track',
    description: 'Sound track resource',
    category: ResourceCategory.SOUND,
    dependencies: ['.wav', '.mp3', '.ogg'],
    mimeTypes: ['audio/wav', 'audio/mpeg', 'audio/ogg'],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: true,
    customDataSchema: {
      duration: {
        type: 'number',
        description: 'Duration in seconds',
        required: true
      },
      sampleRate: {
        type: 'number',
        description: 'Sample rate in Hz',
        required: true
      }
    }
  },
  [0x0C772E27]: { // SCRIPT (not in S4TK enum)
    type: 0x0C772E27,
    name: 'Script',
    description: 'Python script resource',
    category: ResourceCategory.SCRIPT,
    dependencies: ['.py'],
    mimeTypes: ['text/x-python'],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: true,
    customDataSchema: {
      moduleName: {
        type: 'string',
        description: 'Python module name',
        required: true
      },
      dependencies: {
        type: 'array',
        description: 'Module dependencies',
        required: false
      }
    }
  },
  [0x0166038C]: { // TUNING (not in S4TK enum)
    type: 0x0166038C,
    name: 'Tuning',
    description: 'Game tuning XML resource',
    category: ResourceCategory.TUNING,
    dependencies: ['.xml'],
    mimeTypes: ['application/xml'],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: true,
    customDataSchema: {
      instanceId: {
        type: 'string',
        description: 'Tuning instance ID',
        required: true
      },
      tuningType: {
        type: 'string',
        description: 'Type of tuning data',
        required: true
      }
    }
  },
  // Add other types as needed, ensuring category uses ResourceCategory enum
  [0x034AEECB as number]: { // Add the other observed CAS Part type ID, cast to number
    type: 0x034AEECB,
    name: 'CAS Part (Additional)', // Give it a distinct name for clarity
    description: 'Additional Create-a-Sim part resource type',
    category: ResourceCategory.OBJECT,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0x8EAF13DE as number]: { // RIG, cast to number
    type: 0x8EAF13DE,
    name: 'Rig',
    description: 'Animation rig resource',
    category: ResourceCategory.ANIMATION,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0xD3044521 as number]: { // SLOT, cast to number
    type: 0xD3044521,
    name: 'Slot',
    description: 'Object slot resource',
    category: ResourceCategory.OBJECT,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0x3B4C61D as number]: { // Light, cast to number
    type: 0x3B4C61D,
    name: 'Light',
    description: 'Light resource',
    category: ResourceCategory.TUNING,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0x81CA1A10 as number]: { // UNKNOWN_TUNING_B, cast to number
    type: 0x81CA1A10,
    name: 'TuningB', // Keeping previous name for now
    description: 'Unknown tuning resource type B',
    category: ResourceCategory.TUNING,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0x3C2A8647 as number]: { // BUILD_BUY_THUMBNAIL, cast to number
    type: 0x3C2A8647,
    name: 'BuildBuyThumbnail',
    description: 'Build/Buy thumbnail image resource',
    category: ResourceCategory.IMAGE,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0x220557DA as number]: { // STRING_TABLE, cast to number
    type: 0x220557DA,
    name: 'StringTable',
    description: 'String table resource',
    category: ResourceCategory.TUNING,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0x15a1849 as number]: { // RegionMap, cast to number
    type: 0x15a1849,
    name: 'RegionMap (Tuning)', // Clarified name
    description: 'Region map tuning resource',
    category: ResourceCategory.TUNING,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0x3453cf95 as number]: { // Rle2Image, cast to number
    type: 0x3453cf95,
    name: 'Rle2Image',
    description: 'RLE2 compressed image resource',
    category: ResourceCategory.IMAGE,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0x319e4f1d as number]: { // DstImage, cast to number
    type: 0x319e4f1d,
    name: 'DstImage',
    description: 'Destination image resource',
    category: ResourceCategory.IMAGE,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0xb2d882 as number]: { // Generic Image, cast to number
    type: 0xb2d882,
    name: 'GenericImage',
    description: 'Generic image resource',
    category: ResourceCategory.IMAGE,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0xAC16FBEC as number]: { // Added RegionMap based on search results
    type: 0xAC16FBEC,
    name: 'RegionMap',
    description: 'Controls GEOM rendering order',
    category: ResourceCategory.OBJECT,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0xba856c78 as number]: { // Added Specular/Cubemap based on search results
    type: 0xba856c78,
    name: 'Specular/Cubemap',
    description: 'Specular or cubemap image resource',
    category: ResourceCategory.IMAGE,
    dependencies: [],
    mimeTypes: [],
    isCompressible: false, // Specular maps are typically not compressed
    isEncryptable: false,
    isModifiable: true,
    hasCustomData: false
  },
  [0x545AC67A as number]: { // Added SimData based on search results
    type: 0x545AC67A,
    name: 'SimData',
    description: 'SimData resource',
    category: ResourceCategory.TUNING,
    dependencies: [],
    mimeTypes: [],
    isCompressible: false, // SimData is typically not compressed
    isEncryptable: false,
    isModifiable: true,
    hasCustomData: true // SimData has structured data
  },
  [0x6017E896 as number]: { // Added XML Tuning based on search results
    type: 0x6017E896,
    name: 'XML Tuning',
    description: 'XML tuning resource',
    category: ResourceCategory.TUNING,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: true
  },
  [0x3c1d8799 as number]: { // Added based on search results
    type: 0x3c1d8799,
    name: 'XML (Unknown)', // Categorized as TUNING based on search results
    description: 'Unknown XML resource type 3C1D8799',
    category: ResourceCategory.TUNING,
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
  [0x339bc5bd as number]: { // Added based on search results
    type: 0x339bc5bd,
    name: 'Unknown339BC5BD', // Placeholder name
    description: 'Unknown resource type 339BC5BD',
    category: ResourceCategory.OBJECT, // Categorized as OBJECT based on context
    dependencies: [],
    mimeTypes: [],
    isCompressible: true,
    isEncryptable: true,
    isModifiable: true,
    hasCustomData: false
  },
};

export function getResourceTypeMetadata(type: BinaryResourceType): ResourceTypeMetadata {
  return RESOURCE_TYPE_METADATA[type as number] || RESOURCE_TYPE_METADATA[0]!; // Use 0 for unknown fallback
}

export function getResourceTypeDescription(type: BinaryResourceType): string {
  const metadata = getResourceTypeMetadata(type);
  return metadata ? metadata.description : 'Unknown resource type';
}
