/**
 * Command-line argument parser for test scripts
 *
 * This module provides utilities for parsing command-line arguments.
 */

// Default Sims 4 mods folder path
export const DEFAULT_MODS_PATH = 'C:\\Users\\<USER>\\OneDrive\\Documents\\Electronic Arts\\The Sims 4\\Mods';

/**
 * Command-line arguments
 */
export interface CommandLineArgs {
    modsPath: string;
    maxPackages: number;
    analyzeTS4Scripts: boolean;
    parallelProcessing: boolean;
    maxConcurrentTasks: number;
    memoryLimit: number;
    logContentConflicts: boolean;
    logLevel: string;
    // Hybrid approach options
    directBufferThreshold: number;
    chunkedProcessingThreshold: number;
    testMode: string; // 'normal', 'progressive', 'stress'
    // Streaming pipeline test options
    testStreamingPipeline: boolean;
    testProgressiveStreaming: boolean;
    simulateHardwareProfile: string; // 'low-end', 'mid-range', 'high-end'
    injectErrors: boolean;
    corruptData: boolean;
    packageCounts: number[]; // Array of package counts for progressive testing
    modCount: number; // Number of mods to process in a single test
    // Semantic conflict detection test options
    testSemanticConflictDetection: boolean;
    enableDependencyGraphAnalysis: boolean;
    enableSemanticDetection: boolean;
    logDetailedConflicts: boolean;
    useInMemoryDatabase: boolean;
    // Intelligent conflict detection test options
    testIntelligentConflictDetection: boolean;
    testTraitConflicts: boolean;
    testBuffConflicts: boolean;
    testSeverityClassification: boolean;
    testFalsePositiveReduction: boolean;
    testConfigurationHandling: boolean;
    testPrioritySorting: boolean;
    // Package analyzer implementation
    analyzerImplementation: string; // 'legacy', 'streaming', 'consolidated'
    // Phase 1 improvements test options
    testEnhancedMetadata: boolean;
    testIntelligentConflicts: boolean;
    testPerformanceOptimizer: boolean;
    testPredictiveAnalysis: boolean;
    testPhase1Integration: boolean;
    // Comprehensive test suite options
    testComprehensive: boolean;
    enableProgressiveScaling: boolean;
    enableConcurrencyTesting: boolean;
    enableMemoryStressTesting: boolean;
    enableConflictDetectionTesting: boolean;
    enableStreamingPipelineTesting: boolean;
    enableLargeScaleTesting: boolean;
    // Priority Actions for Real Data Testing
    testRealDatabaseServices: boolean;
    testProgressiveScales: boolean;
    testRealConflictValidation: boolean;
    testGameFileValidation: boolean;
    progressiveScales: number[]; // Array of scales for progressive testing
    enableRealConflictScenarios: boolean;
    // Workflow Orchestration Testing
    testPlayerWorkflow: string; // Specific workflow scenario to test
    testPersona: string; // Player persona to simulate
    testAllWorkflows: boolean; // Run comprehensive workflow suite
    benchmarkPerformance: boolean; // Enable performance benchmarking
    aiInterface: boolean; // Enable AI-compatible output
    outputFormat: string; // Output format: human, json, yaml
}

/**
 * Parse command-line arguments
 * @param args Command-line arguments
 * @returns Parsed arguments
 */
export function parseArgs(args: string[]): CommandLineArgs {
    let modsPath = DEFAULT_MODS_PATH;
    let maxPackages = 1; // Reduced to 1 for testing LSH conflict detection
    let analyzeTS4Scripts = false; // Disable TS4Script analysis for now
    let parallelProcessing = false; // Disable parallel processing for now
    let maxConcurrentTasks = 1; // Reduced to 1 for better memory management
    let memoryLimit = 256 * 1024 * 1024; // Reduced to 256 MB
    let logContentConflicts = false; // Default to false
    let logLevel = 'info'; // Default log level
    // Hybrid approach options
    let directBufferThreshold = 5 * 1024 * 1024; // 5MB default
    let chunkedProcessingThreshold = 50 * 1024 * 1024; // 50MB default
    let testMode = 'normal'; // Default test mode
    // Streaming pipeline test options
    let testStreamingPipeline = false; // Default to false
    let testProgressiveStreaming = false; // Default to false
    let simulateHardwareProfile = 'mid-range'; // Default to mid-range
    let injectErrors = false; // Default to false
    let corruptData = false; // Default to false
    let packageCounts = [5, 10, 25, 50, 100]; // Default package counts for progressive testing
    let modCount = 5; // Default number of mods to process in a single test
    // Semantic conflict detection test options
    let testSemanticConflictDetection = false; // Default to false
    let enableDependencyGraphAnalysis = true; // Default to true
    let enableSemanticDetection = true; // Default to true
    let logDetailedConflicts = true; // Default to true
    let useInMemoryDatabase = true; // Default to true
    // Intelligent conflict detection test options
    let testIntelligentConflictDetection = false; // Default to false
    let testTraitConflicts = true; // Default to true
    let testBuffConflicts = true; // Default to true
    let testSeverityClassification = true; // Default to true
    let testFalsePositiveReduction = true; // Default to true
    let testConfigurationHandling = true; // Default to true
    let testPrioritySorting = true; // Default to true
    // Package analyzer implementation
    let analyzerImplementation = 'streaming'; // Default to streaming
    // Phase 1 improvements test options
    let testEnhancedMetadata = false; // Default to false
    let testIntelligentConflicts = false; // Default to false
    let testPerformanceOptimizer = false; // Default to false
    let testPredictiveAnalysis = false; // Default to false
    let testPhase1Integration = false; // Default to false
    // Comprehensive test suite options
    let testComprehensive = false; // Default to false
    let enableProgressiveScaling = true; // Default to true
    let enableConcurrencyTesting = true; // Default to true
    let enableMemoryStressTesting = true; // Default to true
    let enableConflictDetectionTesting = true; // Default to true
    let enableStreamingPipelineTesting = true; // Default to true
    let enableLargeScaleTesting = false; // Default to false (requires large datasets)
    // Priority Actions for Real Data Testing
    let testRealDatabaseServices = false; // Default to false
    let testProgressiveScales = false; // Default to false
    let testRealConflictValidation = false; // Default to false
    let testGameFileValidation = false; // Default to false
    let progressiveScales = [10, 25, 50, 100, 250, 500]; // Default progressive scales
    let enableRealConflictScenarios = false; // Default to false
    // Workflow Orchestration Testing
    let testPlayerWorkflow = ''; // Default to empty (no specific workflow)
    let testPersona = ''; // Default to empty (no specific persona)
    let testAllWorkflows = false; // Default to false
    let benchmarkPerformance = false; // Default to false
    let aiInterface = false; // Default to false
    let outputFormat = 'human'; // Default to human-readable output

    for (let i = 0; i < args.length; i++) {
        if (args[i] === '--mods-path' && i + 1 < args.length) {
            modsPath = args[i + 1];
            i++;
        } else if (args[i] === '--max-packages' && i + 1 < args.length) {
            maxPackages = parseInt(args[i + 1], 10);
            i++;
        } else if (args[i] === '--no-ts4script') {
            analyzeTS4Scripts = false;
        } else if (args[i] === '--no-parallel') {
            parallelProcessing = false;
        } else if (args[i] === '--max-concurrent-tasks' && i + 1 < args.length) {
            maxConcurrentTasks = parseInt(args[i + 1], 10);
            i++;
        } else if (args[i] === '--memory-limit' && i + 1 < args.length) {
            // Parse memory limit (accepts values like 2GB, 1536MB, etc.)
            const memLimitStr = args[i + 1].toLowerCase();
            let memLimit = 0;

            if (memLimitStr.endsWith('gb')) {
                memLimit = parseInt(memLimitStr.slice(0, -2), 10) * 1024 * 1024 * 1024;
            } else if (memLimitStr.endsWith('mb')) {
                memLimit = parseInt(memLimitStr.slice(0, -2), 10) * 1024 * 1024;
            } else if (memLimitStr.endsWith('kb')) {
                memLimit = parseInt(memLimitStr.slice(0, -2), 10) * 1024;
            } else {
                memLimit = parseInt(memLimitStr, 10);
            }

            if (memLimit > 0) {
                memoryLimit = memLimit;
            }

            i++;
        } else if (args[i] === '--log-content-conflicts') {
            logContentConflicts = true;
        } else if (args[i] === '--log-level' && i + 1 < args.length) {
            logLevel = args[i + 1];
            i++;
        } else if (args[i] === '--direct-buffer-threshold' && i + 1 < args.length) {
            // Parse threshold (accepts values like 10MB, 5MB, etc.)
            const thresholdStr = args[i + 1].toLowerCase();
            let threshold = 0;

            if (thresholdStr.endsWith('gb')) {
                threshold = parseInt(thresholdStr.slice(0, -2), 10) * 1024 * 1024 * 1024;
            } else if (thresholdStr.endsWith('mb')) {
                threshold = parseInt(thresholdStr.slice(0, -2), 10) * 1024 * 1024;
            } else if (thresholdStr.endsWith('kb')) {
                threshold = parseInt(thresholdStr.slice(0, -2), 10) * 1024;
            } else {
                threshold = parseInt(thresholdStr, 10);
            }

            if (threshold > 0) {
                directBufferThreshold = threshold;
            }
            i++;
        } else if (args[i] === '--chunked-threshold' && i + 1 < args.length) {
            // Parse threshold (accepts values like 50MB, 100MB, etc.)
            const thresholdStr = args[i + 1].toLowerCase();
            let threshold = 0;

            if (thresholdStr.endsWith('gb')) {
                threshold = parseInt(thresholdStr.slice(0, -2), 10) * 1024 * 1024 * 1024;
            } else if (thresholdStr.endsWith('mb')) {
                threshold = parseInt(thresholdStr.slice(0, -2), 10) * 1024 * 1024;
            } else if (thresholdStr.endsWith('kb')) {
                threshold = parseInt(thresholdStr.slice(0, -2), 10) * 1024;
            } else {
                threshold = parseInt(thresholdStr, 10);
            }

            if (threshold > 0) {
                chunkedProcessingThreshold = threshold;
            }
            i++;
        } else if (args[i] === '--test-mode' && i + 1 < args.length) {
            const mode = args[i + 1].toLowerCase();
            if (['normal', 'progressive', 'stress'].includes(mode)) {
                testMode = mode;
            }
            i++;
        } else if (args[i] === '--test-streaming-pipeline') {
            testStreamingPipeline = true;
        } else if (args[i] === '--test-progressive-streaming') {
            testProgressiveStreaming = true;
        } else if (args[i] === '--hardware-profile' && i + 1 < args.length) {
            const profile = args[i + 1].toLowerCase();
            if (['low-end', 'mid-range', 'high-end'].includes(profile)) {
                simulateHardwareProfile = profile;
            }
            i++;
        } else if (args[i] === '--package-counts' && i + 1 < args.length) {
            try {
                // Parse comma-separated list of package counts
                const countsStr = args[i + 1];
                const counts = countsStr.split(',').map(c => parseInt(c.trim(), 10));

                // Filter out invalid values and sort in ascending order
                const validCounts = counts.filter(c => !isNaN(c) && c > 0).sort((a, b) => a - b);

                if (validCounts.length > 0) {
                    packageCounts = validCounts;
                }
            } catch (error) {
                console.error(`Error parsing package counts: ${error.message}`);
            }
            i++;
        } else if (args[i] === '--inject-errors') {
            injectErrors = true;
        } else if (args[i] === '--corrupt-data') {
            corruptData = true;
        } else if (args[i] === '--mod-count' && i + 1 < args.length) {
            modCount = parseInt(args[i + 1], 10);
            i++;
        } else if (args[i] === '--test-semantic-conflict-detection') {
            testSemanticConflictDetection = true;
        } else if (args[i] === '--no-dependency-graph-analysis') {
            enableDependencyGraphAnalysis = false;
        } else if (args[i] === '--no-semantic-detection') {
            enableSemanticDetection = false;
        } else if (args[i] === '--no-detailed-conflicts') {
            logDetailedConflicts = false;
        } else if (args[i] === '--no-in-memory-database') {
            useInMemoryDatabase = false;
        } else if (args[i] === '--test-intelligent-conflict-detection') {
            testIntelligentConflictDetection = true;
        } else if (args[i] === '--no-trait-conflicts') {
            testTraitConflicts = false;
        } else if (args[i] === '--no-buff-conflicts') {
            testBuffConflicts = false;
        } else if (args[i] === '--no-severity-classification') {
            testSeverityClassification = false;
        } else if (args[i] === '--no-false-positive-reduction') {
            testFalsePositiveReduction = false;
        } else if (args[i] === '--no-configuration-handling') {
            testConfigurationHandling = false;
        } else if (args[i] === '--no-priority-sorting') {
            testPrioritySorting = false;
        } else if (args[i] === '--analyzer-implementation' && i + 1 < args.length) {
            const implementation = args[i + 1].toLowerCase();
            if (['legacy', 'streaming', 'consolidated'].includes(implementation)) {
                analyzerImplementation = implementation;
            }
            i++;
        } else if (args[i] === '--test-enhanced-metadata') {
            testEnhancedMetadata = true;
        } else if (args[i] === '--test-intelligent-conflicts') {
            testIntelligentConflicts = true;
        } else if (args[i] === '--test-performance-optimizer') {
            testPerformanceOptimizer = true;
        } else if (args[i] === '--test-predictive-analysis') {
            testPredictiveAnalysis = true;
        } else if (args[i] === '--test-phase1-integration') {
            testPhase1Integration = true;
        } else if (args[i] === '--test-comprehensive') {
            testComprehensive = true;
        } else if (args[i] === '--no-progressive-scaling') {
            enableProgressiveScaling = false;
        } else if (args[i] === '--no-concurrency-testing') {
            enableConcurrencyTesting = false;
        } else if (args[i] === '--no-memory-stress-testing') {
            enableMemoryStressTesting = false;
        } else if (args[i] === '--no-conflict-detection-testing') {
            enableConflictDetectionTesting = false;
        } else if (args[i] === '--no-streaming-pipeline-testing') {
            enableStreamingPipelineTesting = false;
        } else if (args[i] === '--enable-large-scale-testing') {
            enableLargeScaleTesting = true;
        } else if (args[i] === '--test-real-database-services') {
            testRealDatabaseServices = true;
        } else if (args[i] === '--test-progressive-scales') {
            testProgressiveScales = true;
        } else if (args[i] === '--test-real-conflict-validation') {
            testRealConflictValidation = true;
        } else if (args[i] === '--test-game-file-validation') {
            testGameFileValidation = true;
        } else if (args[i] === '--progressive-scales' && i + 1 < args.length) {
            try {
                // Parse comma-separated list of progressive scales
                const scalesStr = args[i + 1];
                const scales = scalesStr.split(',').map(s => parseInt(s.trim(), 10));

                // Filter out invalid values and sort in ascending order
                const validScales = scales.filter(s => !isNaN(s) && s > 0).sort((a, b) => a - b);

                if (validScales.length > 0) {
                    progressiveScales = validScales;
                }
            } catch (error) {
                console.error(`Error parsing progressive scales: ${error.message}`);
            }
            i++;
        } else if (args[i] === '--enable-real-conflict-scenarios') {
            enableRealConflictScenarios = true;
        } else if (args[i] === '--test-player-workflow' && i + 1 < args.length) {
            testPlayerWorkflow = args[i + 1];
            i++;
        } else if (args[i] === '--test-persona' && i + 1 < args.length) {
            testPersona = args[i + 1];
            i++;
        } else if (args[i] === '--test-all-workflows') {
            testAllWorkflows = true;
        } else if (args[i] === '--benchmark-performance') {
            benchmarkPerformance = true;
        } else if (args[i] === '--ai-interface') {
            aiInterface = true;
        } else if (args[i] === '--output-format' && i + 1 < args.length) {
            const format = args[i + 1].toLowerCase();
            if (['human', 'json', 'yaml'].includes(format)) {
                outputFormat = format;
            }
            i++;
        } else if (args[i] === '--help') {
            printHelp();
            process.exit(0);
        }
    }

    return {
        modsPath,
        maxPackages,
        analyzeTS4Scripts,
        parallelProcessing,
        maxConcurrentTasks,
        memoryLimit,
        logContentConflicts,
        logLevel,
        directBufferThreshold,
        chunkedProcessingThreshold,
        testMode,
        testStreamingPipeline,
        testProgressiveStreaming,
        simulateHardwareProfile,
        injectErrors,
        corruptData,
        packageCounts,
        modCount,
        testSemanticConflictDetection,
        enableDependencyGraphAnalysis,
        enableSemanticDetection,
        logDetailedConflicts,
        useInMemoryDatabase,
        testIntelligentConflictDetection,
        testTraitConflicts,
        testBuffConflicts,
        testSeverityClassification,
        testFalsePositiveReduction,
        testConfigurationHandling,
        testPrioritySorting,
        analyzerImplementation,
        testEnhancedMetadata,
        testIntelligentConflicts,
        testPerformanceOptimizer,
        testPredictiveAnalysis,
        testPhase1Integration,
        testComprehensive,
        enableProgressiveScaling,
        enableConcurrencyTesting,
        enableMemoryStressTesting,
        enableConflictDetectionTesting,
        enableStreamingPipelineTesting,
        enableLargeScaleTesting,
        testRealDatabaseServices,
        testProgressiveScales,
        testRealConflictValidation,
        testGameFileValidation,
        progressiveScales,
        enableRealConflictScenarios,
        testPlayerWorkflow,
        testPersona,
        testAllWorkflows,
        benchmarkPerformance,
        aiInterface,
        outputFormat
    };
}

/**
 * Print help message
 */
export function printHelp(): void {
    console.log(`
Usage: npx tsx --no-warnings src/tools/test-real-world-refactored.ts [options]

Options:
  --mods-path <path>              Path to the Sims 4 mods folder (default: ${DEFAULT_MODS_PATH})
  --max-packages <num>            Maximum number of packages to analyze (default: 1)
  --no-ts4script                  Skip TS4Script analysis (default: disabled)
  --no-parallel                   Disable parallel processing (default: disabled)
  --max-concurrent-tasks <num>    Maximum number of concurrent tasks (default: 1)
  --memory-limit <size>           Memory limit (e.g., 1GB, 512MB, default: 256MB)
  --log-content-conflicts         Enable detailed logging for content conflict detection
  --log-level <level>             Set logging level (debug, info, warn, error) (default: info)
  --direct-buffer-threshold <size> Maximum size for direct buffer approach (default: 5MB)
  --chunked-threshold <size>      Maximum size for chunked processing (default: 50MB)
  --test-mode <mode>              Test mode: normal, progressive, stress (default: normal)
  --test-streaming-pipeline       Test the streaming pipeline components
  --test-progressive-streaming    Run progressive tests with increasing numbers of package files
  --hardware-profile <profile>    Simulate hardware profile: low-end, mid-range, high-end (default: mid-range)
  --package-counts <counts>       Comma-separated list of package counts for progressive testing (default: 5,10,25,50,100)
  --inject-errors                 Inject errors during streaming pipeline tests
  --corrupt-data                  Corrupt data during streaming pipeline tests
  --mod-count <num>               Number of mods to process in a single test (default: 5)
  --test-semantic-conflict-detection Test semantic conflict detection with dependency graph integration
  --no-dependency-graph-analysis  Disable dependency graph analysis (default: enabled)
  --no-semantic-detection         Disable semantic conflict detection (default: enabled)
  --no-detailed-conflicts         Disable detailed conflict logging (default: enabled)
  --no-in-memory-database         Use persistent database instead of in-memory database (default: in-memory)
  --test-intelligent-conflict-detection Test intelligent conflict detection (Phase 2)
  --no-trait-conflicts            Disable trait conflict detection tests (default: enabled)
  --no-buff-conflicts             Disable buff conflict detection tests (default: enabled)
  --no-severity-classification    Disable severity classification tests (default: enabled)
  --no-false-positive-reduction   Disable false positive reduction tests (default: enabled)
  --no-configuration-handling     Disable configuration handling tests (default: enabled)
  --no-priority-sorting           Disable priority sorting tests (default: enabled)
  --analyzer-implementation <impl> Package analyzer implementation: legacy, streaming, consolidated (default: streaming)
  --test-enhanced-metadata        Test Phase 1 Enhanced Metadata Extractor
  --test-intelligent-conflicts    Test Phase 1 Intelligent Conflict Detector
  --test-performance-optimizer    Test Phase 1 Performance Optimizer
  --test-predictive-analysis      Test Phase 1 Predictive Conflict Analyzer
  --test-phase1-integration       Test Phase 1 full integration
  --test-comprehensive            Run comprehensive test suite covering all system components
  --no-progressive-scaling        Disable progressive scaling tests (default: enabled)
  --no-concurrency-testing        Disable concurrency testing (default: enabled)
  --no-memory-stress-testing      Disable memory stress testing (default: enabled)
  --no-conflict-detection-testing Disable conflict detection testing (default: enabled)
  --no-streaming-pipeline-testing Disable streaming pipeline testing (default: enabled)
  --enable-large-scale-testing    Enable large-scale testing with 10,000+ resources (default: disabled)
  --test-real-database-services   Test real database services instead of mocks (Priority Action 1)
  --test-progressive-scales       Test with progressively larger mod collections (Priority Action 2)
  --test-real-conflict-validation Test conflict detection against known real scenarios (Priority Action 3)
  --test-game-file-validation     Test compatibility with real Sims 4 game files (Priority Action 5)
  --progressive-scales <scales>   Comma-separated list of scales for progressive testing (default: 10,25,50,100,250,500)
  --enable-real-conflict-scenarios Enable testing with known problematic mod combinations
  --test-player-workflow <path>   Test specific player workflow scenario (Priority Action 4)
  --test-persona <persona>        Test with specific player persona: novice, experienced, power_user, troubleshooter
  --test-all-workflows            Run comprehensive workflow suite with all scenarios
  --benchmark-performance         Enable performance benchmarking during workflow tests
  --ai-interface                  Enable AI-compatible output format
  --output-format <format>        Output format: human, json, yaml (default: human)
  --help                          Show this help message
    `);
}
