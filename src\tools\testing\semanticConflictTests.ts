/**
 * Semantic Conflict Detection Tests
 *
 * This module provides functions for testing the semantic conflict detection system
 * with dependency graph integration and comprehensive memory management.
 */

import { DatabaseService } from '../../services/databaseService.js';
import { ConflictDetector, ConflictDetectionOptions } from '../../services/conflict/conflictDetector.js';
import { ResourceInfo } from '../../types/database.js';
import { ConflictSeverity, ConflictType } from '../../types/conflict/index.js';
import { Logger } from '../../utils/logging/logger.js';
import EnhancedMemoryManager from '../../utils/memory/enhancedMemoryManager.js';
import { ResourceTracker, ResourceType, ResourceState } from '../../utils/memory/resourceTracker.js';
import resourcePool from '../../utils/resource/resourcePoolManager.js';
import { findPackageFiles } from './fileScanner.js';
import { createInMemoryDatabase } from './databaseHelper.js';
import { extractResourcesFromPackage } from './packageExtractor.js';
import { formatBytes } from '../../utils/formatting/formatUtils.js';
import path from 'path';

// Using formatBytes from utils/formatting/formatUtils.js

/**
 * Log memory usage information
 * @param memoryManager Memory manager instance
 * @param print Print function
 * @param label Label for the log entry
 */
function logMemoryUsage(memoryManager: EnhancedMemoryManager, print: (message: string) => void, label: string): void {
    const memStats = memoryManager.getMemoryStats();

    print(`${label}:`);
    print(`  Heap used: ${formatBytes(memStats.heapUsed)} / ${formatBytes(memStats.heapTotal)} (${Math.round(memStats.usedPercentage)}%)`);
    print(`  RSS: ${formatBytes(memStats.rss)}`);
    print(`  External: ${formatBytes(memStats.external)}`);
    print(`  ArrayBuffers: ${formatBytes(memStats.arrayBuffers)}`);
    print(`  Memory pressure: ${(memoryManager.getMemoryPressure() * 100).toFixed(1)}%`);
}

/**
 * Options for semantic conflict detection testing
 */
export interface SemanticConflictTestOptions {
    /**
     * Maximum number of packages to test
     * Default: 10
     */
    maxPackages?: number;

    /**
     * Whether to enable dependency graph analysis
     * Default: true
     */
    enableDependencyGraphAnalysis?: boolean;

    /**
     * Whether to enable semantic conflict detection
     * Default: true
     */
    enableSemanticDetection?: boolean;

    /**
     * Whether to log detailed conflict information
     * Default: true
     */
    logDetailedConflicts?: boolean;

    /**
     * Log level for the test
     * Default: 'info'
     */
    logLevel?: 'debug' | 'info' | 'warn' | 'error';

    /**
     * Whether to use in-memory database
     * Default: true
     */
    useInMemoryDatabase?: boolean;
}

/**
 * Test semantic conflict detection with dependency graph integration and comprehensive memory management
 * @param modsPath Path to mods folder
 * @param options Test options
 * @returns Promise resolving to test results
 */
export async function testSemanticConflictDetection(
    modsPath: string,
    options: SemanticConflictTestOptions = {}
): Promise<any> {
    // Initialize memory management
    const memoryManager = EnhancedMemoryManager.getInstance();
    const resourceTracker = ResourceTracker.getInstance();

    // Track this test operation
    const testId = `semantic_conflict_test_${Date.now()}`;
    memoryManager.trackResource('conflictTest', testId);

    // Create logger
    const logger = new Logger('SemanticConflictTest', options.logLevel || 'info');
    const print = (message: string) => process.stdout.write(`${message}\n`);

    // Log initial memory state
    logMemoryUsage(memoryManager, print, 'Initial memory state');

    // Set default options with adaptive batch sizing based on memory pressure
    const memoryPressure = memoryManager.getMemoryPressure();
    const testOptions: Required<SemanticConflictTestOptions> = {
        maxPackages: options.maxPackages || calculateMaxPackages(memoryPressure),
        enableDependencyGraphAnalysis: options.enableDependencyGraphAnalysis !== false,
        enableSemanticDetection: options.enableSemanticDetection !== false,
        logDetailedConflicts: options.logDetailedConflicts !== false,
        logLevel: options.logLevel || 'info',
        useInMemoryDatabase: options.useInMemoryDatabase !== false
    };

    print(`\n===== SEMANTIC CONFLICT DETECTION TEST =====`);
    print(`Memory pressure: ${(memoryPressure * 100).toFixed(2)}%`);
    print(`Max packages: ${testOptions.maxPackages}`);
    print(`Enable dependency graph analysis: ${testOptions.enableDependencyGraphAnalysis}`);
    print(`Enable semantic detection: ${testOptions.enableSemanticDetection}`);
    print(`Log detailed conflicts: ${testOptions.logDetailedConflicts}`);
    print(`Log level: ${testOptions.logLevel}`);
    print(`Use in-memory database: ${testOptions.useInMemoryDatabase}`);

    // Create database service
    let databaseService: DatabaseService;
    try {
        if (testOptions.useInMemoryDatabase) {
            print(`Creating in-memory database...`);
            databaseService = await createInMemoryDatabase();
        } else {
            print(`Using persistent database...`);
            databaseService = new DatabaseService(':memory:', logger);
            await databaseService.initialize();
        }

        // Track database service for cleanup
        resourceTracker.trackResource(
            ResourceType.DATABASE,
            'semanticConflictTest',
            async () => {
                await databaseService.close();
            },
            {
                id: `database_${testId}`,
                state: ResourceState.ACTIVE,
                metadata: { inMemory: testOptions.useInMemoryDatabase }
            }
        );

        // Log memory state after database creation
        logMemoryUsage(memoryManager, print, 'Memory state after database creation');
    } catch (error: any) {
        print(`Error creating database: ${error.message}`);
        memoryManager.untrackResource('conflictTest', testId);
        throw error;
    }

    try {
        // Find package files
        print(`Finding package files in ${modsPath}...`);
        const packageFiles = await findPackageFiles(modsPath, {
            maxFiles: testOptions.maxPackages,
            maxDepth: 3,
            randomize: true,
            progressCallback: (count, _filePath) => {
                if (count % 10 === 0 || count === 1) {
                    print(`Found ${count} package files...`);
                }
            }
        });

        if (packageFiles.length === 0) {
            print(`No package files found in ${modsPath}.`);
            return { success: false, error: 'No package files found' };
        }

        print(`Found ${packageFiles.length} package files.`);

        // Extract resources from packages
        print(`Extracting resources from packages...`);
        let totalResourcesExtracted = 0;

        // Process packages in batches with adaptive batch sizing based on memory pressure
        let currentBatchSize = calculateBatchSize(memoryManager.getMemoryPressure());
        print(`Initial batch size: ${currentBatchSize} packages (based on memory pressure)`);

        for (let i = 0; i < packageFiles.length;) {
            // Recalculate batch size based on current memory pressure
            const currentMemoryPressure = memoryManager.getMemoryPressure();
            const newBatchSize = calculateBatchSize(currentMemoryPressure);

            // Adjust batch size if memory pressure changed significantly
            if (Math.abs(newBatchSize - currentBatchSize) >= 2) {
                print(`Adjusting batch size from ${currentBatchSize} to ${newBatchSize} due to memory pressure change (${(currentMemoryPressure * 100).toFixed(2)}%)`);
                currentBatchSize = newBatchSize;
            }

            const batchEnd = Math.min(i + currentBatchSize, packageFiles.length);
            const batchFiles = packageFiles.slice(i, batchEnd);

            // Create a batch operation ID for tracking
            const batchId = `batch_${Math.floor(i/currentBatchSize) + 1}_${Date.now()}`;
            memoryManager.trackResource('packageBatch', batchId);

            print(`Processing batch ${Math.floor(i/currentBatchSize) + 1}/${Math.ceil(packageFiles.length/currentBatchSize)}: ${batchFiles.length} packages`);

            try {
                // Process each package in the batch
                for (let j = 0; j < batchFiles.length; j++) {
                    const packageFile = batchFiles[j];
                    const packageName = path.basename(packageFile);
                    print(`Processing package ${i + j + 1}/${packageFiles.length}: ${packageName}`);

                    // Track this package operation
                    const packageId = `package_${packageName}_${Date.now()}`;
                    memoryManager.trackResource('package', packageId);

                    try {
                        // Use resource pool to limit concurrent package processing
                        const extractedResources = await resourcePool.submit(
                            `extract_${packageName}`,
                            async () => {
                                return await extractResourcesFromPackage(packageFile, databaseService, logger);
                            }
                        );

                        totalResourcesExtracted += extractedResources.length;
                        print(`Extracted ${extractedResources.length} resources from ${packageName}`);

                        // Clear the extracted resources to free memory
                        extractedResources.length = 0;
                    } catch (error: any) {
                        print(`Error extracting resources from ${packageName}: ${error.message}`);
                    } finally {
                        // Untrack this package operation
                        memoryManager.untrackResource('package', packageId);
                    }

                    // Check memory pressure after each package
                    const packageMemoryPressure = memoryManager.getMemoryPressure();
                    if (packageMemoryPressure > 0.85) {
                        print(`High memory pressure detected (${(packageMemoryPressure * 100).toFixed(2)}%), forcing garbage collection...`);
                        if (global.gc) {
                            global.gc();
                        }

                        // Wait for memory pressure to decrease
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                }
            } finally {
                // Untrack batch operation
                memoryManager.untrackResource('packageBatch', batchId);

                // Force garbage collection after each batch
                if (global.gc) {
                    print(`Running garbage collection after batch...`);
                    global.gc();
                }

                // Log memory state
                logMemoryUsage(memoryManager, print, `Memory state after batch ${Math.floor(i/currentBatchSize) + 1}`);

                // Release any tracked resources for this batch
                await resourceTracker.releaseResourcesByMetadata({ batch: Math.floor(i/currentBatchSize) + 1 });
            }

            // Move to next batch
            i = batchEnd;
        }

        print(`Total resources extracted: ${totalResourcesExtracted}`);

        // Create conflict detector
        print(`Creating conflict detector...`);
        const conflictDetectorOptions: ConflictDetectionOptions = {
            enableSemanticDetection: testOptions.enableSemanticDetection,
            enableDependencyGraphAnalysis: testOptions.enableDependencyGraphAnalysis,
            dependencyGraphOptions: {
                includeWeakDependencies: true,
                includeResourceMetadata: true,
                maxDepth: 5
            },
            maxResourcesToCompare: totalResourcesExtracted,
            maxConflictsToReturn: 1000,
            sortBySeverity: true,
            deduplicateConflicts: true
        };

        const conflictDetector = new ConflictDetector(databaseService, conflictDetectorOptions, logger);

        // Initialize conflict detector
        print(`Initializing conflict detector...`);
        await conflictDetector.initialize();

        // Detect conflicts using resources from the database
        print(`Detecting conflicts between resources in the database...`);
        const startTime = Date.now();

        // Get resources from the database in batches to reduce memory usage
        const batchSize = 1000;
        let offset = 0;
        let allResources: ResourceInfo[] = [];

        print(`Fetching resources from database in batches of ${batchSize}...`);
        while (true) {
            const resources = await databaseService.getResources(batchSize, offset);
            if (resources.length === 0) {
                break;
            }

            print(`Fetched ${resources.length} resources (offset: ${offset})`);
            allResources.push(...resources);

            offset += batchSize;

            // Break if we've fetched enough resources
            if (offset >= totalResourcesExtracted) {
                break;
            }
        }

        print(`Fetched a total of ${allResources.length} resources from the database`);

        // Run garbage collection before conflict detection
        if (global.gc) {
            print(`Running garbage collection before conflict detection...`);
            global.gc();
        }

        // Log memory usage
        const memoryUsage = process.memoryUsage();
        print(`Memory usage before conflict detection: Heap used ${formatBytes(memoryUsage.heapUsed)} / ${formatBytes(memoryUsage.heapTotal)} (${Math.round(memoryUsage.heapUsed / memoryUsage.heapTotal * 100)}%), RSS: ${formatBytes(memoryUsage.rss)}`);

        // Detect conflicts
        print(`Detecting conflicts between ${allResources.length} resources...`);
        const result = await conflictDetector.detectConflicts(allResources);
        const endTime = Date.now();
        const duration = endTime - startTime;

        // Log results
        print(`\n===== CONFLICT DETECTION RESULTS =====`);
        print(`Conflicts detected: ${result.conflicts.length}`);
        print(`Resources compared: ${result.resourcesCompared}`);
        print(`Time taken: ${duration}ms`);

        // Log severity breakdown
        print(`\nSeverity breakdown:`);
        for (const [severity, count] of Object.entries(result.severityBreakdown)) {
            print(`  ${severity}: ${count}`);
        }

        // Log type breakdown
        print(`\nType breakdown:`);
        for (const [type, count] of Object.entries(result.typeBreakdown)) {
            print(`  ${type}: ${count}`);
        }

        // Log detailed conflict information if enabled
        if (testOptions.logDetailedConflicts && result.conflicts.length > 0) {
            print(`\nDetailed conflict information:`);
            for (let i = 0; i < Math.min(result.conflicts.length, 10); i++) {
                const conflict = result.conflicts[i];
                print(`\nConflict ${i + 1}:`);
                print(`  Type: ${conflict.type}`);
                print(`  Severity: ${conflict.severity}`);
                print(`  Description: ${conflict.description}`);

                if (conflict.recommendations && conflict.recommendations.length > 0) {
                    print(`  Recommendations:`);
                    for (const recommendation of conflict.recommendations) {
                        print(`    - ${recommendation}`);
                    }
                }

                if (conflict.affectedResources && conflict.affectedResources.length > 0) {
                    print(`  Affected resources:`);
                    for (const resource of conflict.affectedResources) {
                        print(`    - Type: 0x${resource.type.toString(16).toUpperCase()}, Group: 0x${resource.group.toString(16).toUpperCase()}, Instance: 0x${resource.instance.toString(16).toUpperCase()}`);
                    }
                }
            }

            if (result.conflicts.length > 10) {
                print(`\n... and ${result.conflicts.length - 10} more conflicts.`);
            }
        }

        // Dispose of conflict detector
        print(`\nDisposing conflict detector...`);
        await conflictDetector.dispose();

        // Return results
        return {
            success: true,
            conflicts: result.conflicts.length,
            resourcesCompared: result.resourcesCompared,
            duration,
            severityBreakdown: result.severityBreakdown,
            typeBreakdown: result.typeBreakdown
        };
    } catch (error: any) {
        print(`Error testing semantic conflict detection: ${error.message}`);
        if (error.stack) {
            print(error.stack);
        }
        return { success: false, error: error.message };
    } finally {
        // Close database connection
        print(`Closing database connection...`);
        await databaseService.close();
    }
}
