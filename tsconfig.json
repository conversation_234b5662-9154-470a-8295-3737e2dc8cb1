{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "NodeNext", "resolveJsonModule": true, "isolatedModules": true, "allowJs": true, "noEmit": false, "baseUrl": "./src", "paths": {"@/*": ["./*"]}, "types": ["node", "jest", "socket.io-client"], "allowSyntheticDefaultImports": true, "sourceMap": true, "declaration": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}