/**
 * Official Sims 4 Resource Types - Extracted from Game Assets
 *
 * This file contains all official resource types from the Sims 4 game,
 * extracted from docs/assets/resources.py. This provides comprehensive
 * coverage of all resource types used by the game.
 *
 * Based on: T:\InGame\Gameplay\Scripts\Core\sims4\resources.py
 * Compiled: 2024-09-27 01:59:05
 */

/**
 * Resource Type Metadata Interface
 * Based on InstanceTuningDefinition from the game
 */
export interface ResourceTypeMetadata {
  /** Resource type name */
  typeName: string;
  /** Plural form of type name */
  typeNamePlural?: string;
  /** File extension for this resource type */
  fileExtension?: string;
  /** Numeric resource type ID */
  resourceType: number;
  /** Manager name for this resource type */
  managerName?: string;
  /** Manager type classification */
  managerType?: string;
  /** Whether to use GUID for references */
  useGuidForRef?: boolean;
  /** Whether this is base game only */
  baseGameOnly?: boolean;
  /** Whether this resource type requires references */
  requireReference?: boolean;
  /** Resource category for organization */
  category: ResourceCategory;
}

/**
 * Enhanced Resource Categories based on game analysis
 */
export enum ResourceCategory {
  // Core game resources
  BINARY = 'BINARY',
  TUNING = 'TUNING',
  SCRIPT = 'SCRIPT',

  // Visual resources
  IMAGE = 'IMAGE',
  TEXTURE = 'TEXTURE',
  MODEL = 'MODEL',
  ANIMATION = 'ANIMATION',
  EFFECT = 'EFFECT',

  // Audio resources
  SOUND = 'SOUND',
  AUDIO = 'AUDIO',

  // Object and world resources
  OBJECT = 'OBJECT',
  WORLD = 'WORLD',
  LOT = 'LOT',
  REGION = 'REGION',

  // CAS (Create-A-Sim) resources
  CASPART = 'CASPART',
  CAS_PRESET = 'CAS_PRESET',

  // Gameplay systems
  GAMEPLAY = 'GAMEPLAY',
  INTERACTION = 'INTERACTION',
  TRAIT = 'TRAIT',
  BUFF = 'BUFF',
  MOOD = 'MOOD',
  SKILL = 'SKILL',
  CAREER = 'CAREER',
  ASPIRATION = 'ASPIRATION',

  // Social and relationship systems
  RELATIONSHIP = 'RELATIONSHIP',
  SOCIAL = 'SOCIAL',

  // Situation and event systems
  SITUATION = 'SITUATION',
  EVENT = 'EVENT',

  // UI and interface
  UI = 'UI',
  MENU = 'MENU',

  // Build/Buy mode
  BUILD = 'BUILD',
  BUY = 'BUY',

  // Expansion pack content
  UNIVERSITY = 'UNIVERSITY',
  SEASONS = 'SEASONS',
  PETS = 'PETS',
  MAGIC = 'MAGIC',
  ECO = 'ECO',

  // System and technical
  SYSTEM = 'SYSTEM',
  DEBUG = 'DEBUG',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Complete Official Sims 4 Resource Types
 * Extracted from game assets with numeric IDs and metadata
 */
export enum OfficialResourceType {
  // === STATIC RESOURCE TYPES (from game engine) ===

  // Core system types
  INVALID = 'INVALID', // 4294967295

  // 3D Models and geometry
  MODEL = 'MODEL', // 23466547
  RIG = 'RIG', // 2393838558
  FOOTPRINT = 'FOOTPRINT', // 3548561239
  SLOT = 'SLOT', // 3540272417

  // Object definitions
  OBJECTDEFINITION = 'OBJECTDEFINITION', // 3235601127
  OBJCATALOG = 'OBJCATALOG', // 832458525
  OBJDEF = 'OBJDEF', // 3625704905

  // Images and textures
  PNG = 'PNG', // 796721156
  TGA = 'TGA', // 796721158
  DDS = 'DDS', // 11720834

  // Animation and state machines
  STATEMACHINE = 'STATEMACHINE', // 47570707
  CLIP = 'CLIP', // 1797309683
  CLIP_HEADER = 'CLIP_HEADER', // 3158986820
  WALKSTYLE = 'WALKSTYLE', // 666901909

  // Audio resources
  PROPX = 'PROPX', // 968010314

  // Video resources
  VP6 = 'VP6', // 929579223

  // Cache and optimization
  BC_CACHE = 'BC_CACHE', // 479834948
  AC_CACHE = 'AC_CACHE', // 3794048034

  // Data formats
  XML = 'XML', // 53690476
  COMBINED_TUNING = 'COMBINED_TUNING', // 1659456824

  // Animation data
  TRACKMASK = 'TRACKMASK', // 53633251

  // Sim and household data
  SIMINFO = 'SIMINFO', // 39769844
  HOUSEHOLD_BINARY = 'HOUSEHOLD_BINARY', // 3015981296
  HOUSEHOLD_DESCRIPTION = 'HOUSEHOLD_DESCRIPTION', // 1923050575

  // CAS (Create-A-Sim) resources
  CASPART = 'CASPART', // 55242443
  SKINTONE = 'SKINTONE', // 55867754

  // World and region data
  REGION_DESCRIPTION = 'REGION_DESCRIPTION', // 3596464121
  WORLD_DESCRIPTION = 'WORLD_DESCRIPTION', // 2793466443
  LOT_DESCRIPTION = 'LOT_DESCRIPTION', // 26488364

  // Build mode resources
  FRIEZE = 'FRIEZE', // 2690089244
  BLOCK = 'BLOCK', // 127102176
  CEILING_RAILING = 'CEILING_RAILING', // 1057772186
  FENCE = 'FENCE', // 68746794
  FLOOR_TRIM = 'FLOOR_TRIM', // 2227319321
  FLOOR_PATTERN = 'FLOOR_PATTERN', // 3036111561
  POOL_TRIM = 'POOL_TRIM', // 2782919923
  ROOF = 'ROOF', // 2448276798
  ROOF_TRIM = 'ROOF_TRIM', // 2956008719
  ROOF_PATTERN = 'ROOF_PATTERN', // 4058889606
  STAIRS = 'STAIRS', // 2585840924
  RAILING = 'RAILING', // 471658999
  WALL = 'WALL', // 2438063804
  WALL_PATTERN = 'WALL_PATTERN', // 3589339425
  HALFWALL_TRIM = 'HALFWALL_TRIM', // 2851789917
  DECOTRIM = 'DECOTRIM', // 332336850

  // Material and style resources
  STYLE = 'STYLE', // 2673671952
  GENERIC_MTX = 'GENERIC_MTX', // 2885921078
  MTX_BUNDLE = 'MTX_BUNDLE', // 2377243942

  // Miscellaneous
  MAGAZINECOLLECTION = 'MAGAZINECOLLECTION', // 1946487583
  GPINI = 'GPINI', // 2249506521
  PLAYLIST = 'PLAYLIST', // 1415235194
  TRAY_METADATA = 'TRAY_METADATA', // 713711138
  LOCATOR = 'LOCATOR', // 1220708729

  // === TUNING RESOURCE TYPES (from game logic) ===

  // Core tuning
  TUNING = 'TUNING', // 62078431
  SNIPPET = 'SNIPPET', // 2113017500

  // Posture and movement
  POSTURE = 'POSTURE', // 2909789983
  SLOT_TYPE = 'SLOT_TYPE', // 1772477092
  SLOT_TYPE_SET = 'SLOT_TYPE_SET', // 1058419973

  // Commodities and statistics
  STATIC_COMMODITY = 'STATIC_COMMODITY', // 1359443523
  STATISTIC = 'STATISTIC', // 865846717

  // Relationships and social
  RELATIONSHIP_BIT = 'RELATIONSHIP_BIT', // 151314192
  SOCIAL_GROUP = 'SOCIAL_GROUP', // 776446212
  TOPIC = 'TOPIC', // 1938713686

  // Object states and definitions
  OBJECT_STATE = 'OBJECT_STATE', // 1526890910
  OBJECT_PART = 'OBJECT_PART', // 1900520272
  OBJECT = 'OBJECT', // 3055412916

  // Recipes and crafting
  RECIPE = 'RECIPE', // 3952605219

  // Game rules and systems
  GAME_RULESET = 'GAME_RULESET', // 3779558936

  // Moods and emotions
  MOOD = 'MOOD', // 3128647864
  BUFF = 'BUFF', // 1612179606

  // Traits and personality
  TRAIT = 'TRAIT', // 3412057543

  // Aspirations and goals
  ASPIRATION = 'ASPIRATION', // 683034229
  ASPIRATION_CATEGORY = 'ASPIRATION_CATEGORY', // 3813727192
  ASPIRATION_TRACK = 'ASPIRATION_TRACK', // 3223387309
  OBJECTIVE = 'OBJECTIVE', // 6899006

  // Tutorials and guidance
  TUTORIAL = 'TUTORIAL', // 3762955427
  TUTORIAL_TIP = 'TUTORIAL_TIP', // 2410930353
  GUIDANCE_TIP = 'GUIDANCE_TIP', // 3567295165

  // Careers and work
  CAREER = 'CAREER', // 1939434475
  CAREER_LEVEL = 'CAREER_LEVEL', // 745582072
  CAREER_TRACK = 'CAREER_TRACK', // **********
  CAREER_EVENT = 'CAREER_EVENT', // **********
  CAREER_GIG = 'CAREER_GIG', // **********

  // Interactions and affordances
  INTERACTION = 'INTERACTION', // **********
  PIE_MENU_CATEGORY = 'PIE_MENU_CATEGORY', // ********

  // Achievements and rewards
  ACHIEVEMENT = 'ACHIEVEMENT', // **********
  ACHIEVEMENT_CATEGORY = 'ACHIEVEMENT_CATEGORY', // *********
  ACHIEVEMENT_COLLECTION = 'ACHIEVEMENT_COLLECTION', // ********
  REWARD = 'REWARD', // **********
  ACCOUNT_REWARD = 'ACCOUNT_REWARD', // **********

  // Services and NPCs
  SERVICE_NPC = 'SERVICE_NPC', // **********

  // Venues and lots
  VENUE = 'VENUE', // **********
  LOT_TUNING = 'LOT_TUNING', // **********

  // Regions and neighborhoods
  REGION = 'REGION', // **********
  STREET = 'STREET', // **********

  // Walk-by and ambient behavior
  WALK_BY = 'WALK_BY', // **********

  // Animation and visual effects
  ANIMATION = 'ANIMATION', // **********
  BALLOON = 'BALLOON', // **********

  // Actions and behaviors
  ACTION = 'ACTION', // *********

  // Situations and events
  SITUATION = 'SITUATION', // **********
  SITUATION_JOB = 'SITUATION_JOB', // **********
  SITUATION_GOAL = 'SITUATION_GOAL', // **********
  SITUATION_GOAL_SET = 'SITUATION_GOAL_SET', // **********

  // AI and strategy
  STRATEGY = 'STRATEGY', // **********
  SIM_FILTER = 'SIM_FILTER', // **********

  // Sim templates and data
  SIM_TEMPLATE = 'SIM_TEMPLATE', // *********
  SIM_INFO_FIXUP = 'SIM_INFO_FIXUP', // 3797424274

  // System and technical
  SUBROOT = 'SUBROOT', // 3086978965
  TAG_SET = 'TAG_SET', // 1228493570
  TEMPLATE_CHOOSER = 'TEMPLATE_CHOOSER', // 1220728301

  // Zone and world management
  ZONE_DIRECTOR = 'ZONE_DIRECTOR', // 4183335058
  ZONE_MODIFIER = 'ZONE_MODIFIER', // 1008568217
  OPEN_STREET_DIRECTOR = 'OPEN_STREET_DIRECTOR', // 1265622724

  // Role and state management
  ROLE_STATE = 'ROLE_STATE', // 239932923

  // Broadcasting and communication
  BROADCASTER = 'BROADCASTER', // 3736796019

  // Away actions and rabbit holes
  AWAY_ACTION = 'AWAY_ACTION', // 2947394632
  RABBIT_HOLE = 'RABBIT_HOLE', // 2976568058

  // === EXPANSION PACK CONTENT ===

  // Royalty and fame system
  ROYALTY = 'ROYALTY', // 938421991

  // Detective and mystery
  DETECTIVE_CLUE = 'DETECTIVE_CLUE', // 1400130038
  NOTEBOOK_ENTRY = 'NOTEBOOK_ENTRY', // 2567109238

  // Reward and currency systems
  BUCKS_PERK = 'BUCKS_PERK', // 3963461902
  TEST_BASED_SCORE = 'TEST_BASED_SCORE', // 1332976878

  // Club system
  CLUB_SEED = 'CLUB_SEED', // 794407991
  CLUB_INTERACTION_GROUP = 'CLUB_INTERACTION_GROUP', // 4195351092

  // Drama and narrative
  DRAMA_NODE = 'DRAMA_NODE', // 626258997
  NARRATIVE = 'NARRATIVE', // **********
  STORY_ARC = 'STORY_ARC', // **********
  STORY_CHAPTER = 'STORY_CHAPTER', // **********

  // Group activities
  ENSEMBLE = 'ENSEMBLE', // **********

  // Business system
  BUSINESS = 'BUSINESS', // **********
  BUSINESS_RULE = 'BUSINESS_RULE', // **********

  // User interface
  USER_INTERFACE_INFO = 'USER_INTERFACE_INFO', // **********

  // Call to action system
  CALL_TO_ACTION = 'CALL_TO_ACTION', // **********

  // Health and sickness
  SICKNESS = 'SICKNESS', // **********

  // Pet and animal system
  BREED = 'BREED', // 874331941

  // CAS menu system
  CAS_MENU_ITEM = 'CAS_MENU_ITEM', // 213537012
  CAS_MENU = 'CAS_MENU', // **********
  CAS_STORIES_QUESTION = 'CAS_STORIES_QUESTION', // 52718493
  CAS_STORIES_ANSWER = 'CAS_STORIES_ANSWER', // **********
  CAS_STORIES_TRAIT_CHOOSER = 'CAS_STORIES_TRAIT_CHOOSER', // **********
  CAS_PREFERENCE_GROUP = 'CAS_PREFERENCE_GROUP', // **********
  CAS_PREFERENCE_CATEGORY = 'CAS_PREFERENCE_CATEGORY', // **********
  CAS_PREFERENCE_ITEM = 'CAS_PREFERENCE_ITEM', // **********

  // Relationship system
  RELATIONSHIP_LOCK = 'RELATIONSHIP_LOCK', // **********

  // Household milestones
  HOUSEHOLD_MILESTONE = 'HOUSEHOLD_MILESTONE', // 963831539

  // Conditional layers
  CONDITIONAL_LAYER = 'CONDITIONAL_LAYER', // **********

  // Seasons expansion
  SEASON = 'SEASON', // **********
  HOLIDAY_DEFINITION = 'HOLIDAY_DEFINITION', // 238120813
  HOLIDAY_TRADITION = 'HOLIDAY_TRADITION', // 1070408838
  WEATHER_EVENT = 'WEATHER_EVENT', // 1476851130
  WEATHER_FORECAST = 'WEATHER_FORECAST', // 1233072753

  // Lot decoration
  LOT_DECORATION = 'LOT_DECORATION', // 4264407467
  LOT_DECORATION_PRESET = 'LOT_DECORATION_PRESET', // 3726571771

  // Headlines and news
  HEADLINE = 'HEADLINE', // 4093714525

  // Magic and spells
  SPELL = 'SPELL', // 523506649

  // University expansion
  UNIVERSITY_COURSE_DATA = 'UNIVERSITY_COURSE_DATA', // 689745854
  UNIVERSITY_MAJOR = 'UNIVERSITY_MAJOR', // 660124491
  UNIVERSITY = 'UNIVERSITY', // 3646477745
  UNIVERSITY_COURSE_SCHEDULE = 'UNIVERSITY_COURSE_SCHEDULE', // 2559322869

  // Lunar cycle system
  LUNAR_CYCLE = 'LUNAR_CYCLE', // 1430862616

  // Whims system
  WHIM = 'WHIM', // 1956251190

  // Clan system
  CLAN = 'CLAN', // 3737052837
  CLAN_VALUE = 'CLAN_VALUE', // 2576273579

  // Developmental milestones
  DEVELOPMENTAL_MILESTONE = 'DEVELOPMENTAL_MILESTONE', // 3307360148

  // Debug and development
  TDESC_DEBUG = 'TDESC_DEBUG', // (no ID specified)
  TUNING_DESCRIPTION = 'TUNING_DESCRIPTION', // 2519486516

  // Unknown resource type
  UNKNOWN = 'UNKNOWN'
}
