import { Logger } from '../../../utils/logging/logger.js';
import { ResourceInfo } from '../../../types/database.js';
import { ConflictInfo, ConflictSeverity, ConflictType } from '../../../types/conflict/index.js';
import { DatabaseService } from '../../databaseService.js';
import { ConflictDetectorBase, ConflictDetectionOptionsBase } from './ConflictDetectorBase.js';
import { LSHFactory, LSHType } from '../../../utils/lsh/LSHFactory.js';
import { createHash } from 'crypto';

/**
 * Options for LSH conflict detection
 */
export interface LSHConflictDetectionOptions extends ConflictDetectionOptionsBase {
    /**
     * Similarity threshold for LSH-based conflict detection
     * Default: 0.7
     */
    similarityThreshold?: number;

    /**
     * Maximum content size for LSH-based conflict detection (in bytes)
     * Default: 1MB
     */
    maxContentSize?: number;

    /**
     * Number of hash functions to use for MinHash
     * Default: 100
     */
    numHashes?: number;

    /**
     * Number of bits to use for SimHash
     * Default: 64
     */
    hashBits?: number;

    /**
     * Random seed for hash functions
     * Default: 42
     */
    seed?: number;

    /**
     * Whether to use bucket-based grouping for optimization
     * Default: true
     */
    useBucketGrouping?: boolean;

    /**
     * Number of buckets for bucket-based grouping
     * Default: 100
     */
    numBuckets?: number;
}

/**
 * LSH-based conflict detector
 * Uses locality-sensitive hashing for efficient conflict detection
 */
export class LSHConflictDetector extends ConflictDetectorBase<LSHConflictDetectionOptions> {
    private contentCache: Map<string, { hash: any, type: LSHType }> = new Map();
    private signatureRepository: any;
    private maxCacheSize: number = 50; // Reduced from 100 to 50 for extreme memory conservation
    private cacheHits: number = 0;
    private cacheMisses: number = 0;
    private lastMemoryCheckTime: number = 0;
    private memoryCheckIntervalMs: number = 5000; // Check memory every 5 seconds

    /**
     * Create a new LSH conflict detector
     * @param databaseService Database service instance
     * @param options Options for LSH conflict detection
     * @param logger Optional logger instance
     */
    constructor(
        databaseService: DatabaseService,
        options: LSHConflictDetectionOptions = {},
        logger?: Logger
    ) {
        // Create options object with defaults
        const mergedOptions: LSHConflictDetectionOptions = {
            enabled: options.enabled !== false,
            excludeTypes: options.excludeTypes || [],
            includeTypes: options.includeTypes || [],
            similarityThreshold: options.similarityThreshold || 0.7,
            maxContentSize: options.maxContentSize || 1024 * 1024, // 1MB
            numHashes: options.numHashes || 100,
            hashBits: options.hashBits || 64,
            seed: options.seed || 42,
            useBucketGrouping: options.useBucketGrouping !== false,
            numBuckets: options.numBuckets || 100,
            ...options
        };

        // Pass merged options to parent constructor
        super(databaseService, mergedOptions, logger);

        this.signatureRepository = databaseService.signatures;

        // Only log if logger is properly initialized
        if (this.logger && typeof this.logger.debug === 'function') {
            this.logger.debug(`LSHConflictDetector created with options: ${JSON.stringify(this.options)}`);
        }
    }

    /**
     * Initialize the LSH conflict detector
     */
    public async initialize(): Promise<void> {
        this.logger.debug('Initializing LSHConflictDetector');
        // Nothing to initialize for now
        return Promise.resolve();
    }

    /**
     * Detect conflicts between two resources using LSH
     * @param resource1 First resource
     * @param resource2 Second resource
     * @returns Conflict info or null if no conflict
     */
    public async detectConflict(resource1: ResourceInfo, resource2: ResourceInfo): Promise<ConflictInfo | null> {
        // Skip if detector is disabled
        if (!this.options.enabled) {
            return null;
        }

        // Skip if resources are from the same package
        if (resource1.packagePath === resource2.packagePath) {
            return null;
        }

        // Skip if resources are of different types
        if (resource1.type !== resource2.type) {
            return null;
        }

        // Skip if resources are excluded
        if (this.shouldSkipResource(resource1) || this.shouldSkipResource(resource2)) {
            return null;
        }

        try {
            // Determine LSH type based on resource type
            const lshType = this.getLSHTypeForResourceType(resource1.type);

            // Generate hashes for both resources
            const hash1 = await this.generateHashForResource(resource1, lshType);
            const hash2 = await this.generateHashForResource(resource2, lshType);

            if (!hash1 || !hash2) {
                return null;
            }

            // Get LSH instance
            const lsh = LSHFactory.getInstance(lshType, {
                numHashes: this.options.numHashes,
                hashBits: this.options.hashBits,
                seed: this.options.seed
            });

            // Calculate similarity
            const similarity = lsh.calculateSimilarity(hash1, hash2);

            // Check if similar
            if (similarity >= this.options.similarityThreshold!) {
                // Create conflict
                return this.createConflict(resource1, resource2, similarity, lshType);
            }

            return null;
        } catch (error: any) {
            this.logger.error(`Error detecting conflict between resources ${resource1.id} and ${resource2.id}: ${error.message || error}`);
            return null;
        }
    }

    /**
     * Determine the appropriate LSH type for a resource type
     * @param resourceType Resource type
     * @returns LSH type to use
     */
    private getLSHTypeForResourceType(resourceType: number): LSHType {
        // Use SimHash for text-based resources
        const textBasedTypes = [
            0x545AC67A, // SIMDATA
            0x0166038C, // NAME_MAP
            0x62E94D38, // COMBINED_TUNING
            0x220557DA, // STRING_TABLE
            0x02D5DF13, // ANIMATION_STATE_MACHINE
        ];

        if (textBasedTypes.includes(resourceType)) {
            return LSHType.SIMHASH;
        }

        // Use MinHash for binary resources
        return LSHType.MINHASH;
    }

    /**
     * Generate a hash for a resource
     * @param resource Resource to hash
     * @param lshType LSH type to use
     * @returns Hash value or null if not possible
     */
    private async generateHashForResource(resource: ResourceInfo, lshType: LSHType): Promise<any> {
        try {
            // Skip if resource has no ID
            if (!resource.id) {
                this.logger.debug(`Resource has no ID, skipping`);
                return null;
            }

            // Check if hash is already in cache
            const cacheKey = `${resource.id}_${lshType}`;
            if (this.contentCache.has(cacheKey)) {
                const cachedHash = this.contentCache.get(cacheKey);
                if (cachedHash) {
                    // Track cache hit
                    this.cacheHits++;
                    return cachedHash.hash;
                }
            }

            // Track cache miss
            this.cacheMisses++;

            // Check if signature is in the database
            const storedSignature = this.signatureRepository.getSignature(resource.id, lshType);
            if (storedSignature) {
                // Add to cache with memory management
                this.addToCache(cacheKey, { hash: storedSignature, type: lshType });
                return storedSignature;
            }

            // Skip if resource has no content
            if (!resource.contentPath && !resource.contentSnippet) {
                this.logger.debug(`Resource ${resource.id} has no content, skipping`);
                return null;
            }

            // Get content from database or content-addressable storage
            const content = await this.getResourceContent(resource);
            if (!content) {
                this.logger.debug(`Could not get content for resource ${resource.id}, skipping`);
                return null;
            }

            // Skip if content is too large
            if (content.length > this.options.maxContentSize!) {
                this.logger.debug(`Content for resource ${resource.id} is too large (${content.length} bytes), skipping`);
                return null;
            }

            // Generate hash based on LSH type
            const lsh = LSHFactory.getInstance(lshType, {
                numHashes: this.options.numHashes,
                hashBits: this.options.hashBits,
                seed: this.options.seed
            });

            const hash = lsh.generateHash(content);

            // Cache the hash with cache size management
            this.addToCache(cacheKey, { hash, type: lshType });

            // Store the signature in the database
            try {
                this.signatureRepository.saveSignature(resource.id, lshType, hash);
                this.logger.debug(`Stored ${lshType} signature for resource ${resource.id}`);
            } catch (storageError: any) {
                this.logger.error(`Error storing signature for resource ${resource.id}: ${storageError.message || storageError}`);
            }

            return hash;
        } catch (error: any) {
            this.logger.error(`Error generating hash for resource ${resource.id}: ${error.message || error}`);
            return null;
        }
    }

    /**
     * Get resource content
     * @param resource Resource info
     * @returns Resource content as Buffer or null if not found
     */
    private async getResourceContent(resource: ResourceInfo): Promise<Buffer | null> {
        try {
            // Check if resource has content snippet
            if (resource.contentSnippet) {
                return Buffer.from(resource.contentSnippet);
            }

            // Check if resource has content path
            if (resource.contentPath) {
                // Get content from content-addressable storage
                const contentStorage = this.databaseService.getContentStorage();
                if (contentStorage) {
                    const content = await contentStorage.getContent(resource.contentPath);
                    if (content) {
                        return content;
                    }
                }
            }

            // Get content from parsed content repository
            const parsedContent = await this.databaseService.parsedContent.getParsedContentByResourceId(resource.id);
            if (parsedContent && parsedContent.content) {
                return Buffer.from(parsedContent.content);
            }

            return null;
        } catch (error: any) {
            this.logger.error(`Error getting content for resource ${resource.id}: ${error.message || error}`);
            return null;
        }
    }

    /**
     * Add an item to the cache with improved memory management
     * @param key Cache key
     * @param value Cache value
     */
    private addToCache(key: string, value: { hash: any, type: LSHType }): void {
        // Check memory pressure before adding to cache
        this.checkMemoryPressure();

        // Check if cache is already at capacity before adding
        if (this.contentCache.size >= this.maxCacheSize) {
            // Remove 50% of entries when cache is full for more aggressive memory management
            const entriesToRemove = Math.floor(this.maxCacheSize * 0.5);
            const keys = Array.from(this.contentCache.keys()).slice(0, entriesToRemove);

            for (const keyToRemove of keys) {
                this.contentCache.delete(keyToRemove);
            }

            this.logger.debug(`Cache size reached limit of ${this.maxCacheSize}, removed ${keys.length} entries (50%)`);

            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
        }

        // Add to cache
        this.contentCache.set(key, value);

        // Log cache statistics periodically
        if ((this.cacheHits + this.cacheMisses) % 100 === 0 && (this.cacheHits + this.cacheMisses) > 0) {
            const hitRate = this.cacheHits / (this.cacheHits + this.cacheMisses);
            this.logger.debug(`Cache statistics: size=${this.contentCache.size}, hits=${this.cacheHits}, misses=${this.cacheMisses}, hit rate=${(hitRate * 100).toFixed(2)}%`);
        }
    }

    /**
     * Check memory pressure and clear cache if needed
     * This helps prevent out-of-memory errors
     */
    private checkMemoryPressure(): void {
        const now = Date.now();

        // Only check memory pressure periodically to avoid performance impact
        if (now - this.lastMemoryCheckTime < this.memoryCheckIntervalMs) {
            return;
        }

        this.lastMemoryCheckTime = now;

        try {
            const memoryUsage = process.memoryUsage();
            const heapUsed = memoryUsage.heapUsed;
            const heapTotal = memoryUsage.heapTotal;
            const memoryPressure = heapUsed / heapTotal;

            // If memory pressure is high, clear part of the cache
            if (memoryPressure > 0.7) {
                const cacheSize = this.contentCache.size;

                // More aggressive clearing under higher pressure
                let clearPercentage = 0.5; // Default 50%

                if (memoryPressure > 0.9) {
                    clearPercentage = 0.9; // Clear 90% under extreme pressure
                    this.logger.warn(`Extreme memory pressure (${(memoryPressure * 100).toFixed(1)}%)`);
                } else if (memoryPressure > 0.8) {
                    clearPercentage = 0.7; // Clear 70% under high pressure
                    this.logger.warn(`High memory pressure (${(memoryPressure * 100).toFixed(1)}%)`);
                }

                const itemsToRemove = Math.ceil(cacheSize * clearPercentage);

                if (itemsToRemove > 0) {
                    this.logger.debug(`Memory pressure (${(memoryPressure * 100).toFixed(1)}%), clearing ${itemsToRemove} items (${(clearPercentage * 100).toFixed(0)}%) from LSH cache`);

                    // Get cache keys
                    const keys = Array.from(this.contentCache.keys()).slice(0, itemsToRemove);

                    // Remove items from cache
                    for (const key of keys) {
                        this.contentCache.delete(key);
                    }

                    // Force garbage collection if available
                    if (global.gc) {
                        global.gc();
                    }
                }
            }
        } catch (error) {
            this.logger.error('Error checking memory pressure:', error);
        }
    }

    /**
     * Create a conflict between two resources
     * @param resource1 First resource
     * @param resource2 Second resource
     * @param similarity Similarity score
     * @param lshType LSH type used
     * @returns Conflict info
     */
    private createConflict(resource1: ResourceInfo, resource2: ResourceInfo, similarity: number, lshType: LSHType): ConflictInfo {
        // Generate a unique ID for the conflict
        const conflictId = createHash('sha256')
            .update(`${resource1.id}_${resource2.id}_${lshType}_${similarity}`)
            .digest('hex');

        // Determine severity based on similarity
        let severity: ConflictSeverity;
        if (similarity >= 0.9) {
            severity = ConflictSeverity.HIGH;
        } else if (similarity >= 0.8) {
            severity = ConflictSeverity.MEDIUM;
        } else {
            severity = ConflictSeverity.LOW;
        }

        // Create conflict info
        return {
            id: conflictId,
            type: ConflictType.CONTENT,
            severity,
            description: `Similar content detected between resources (${similarity.toFixed(2)} similarity)`,
            affectedResources: [
                {
                    type: resource1.type,
                    group: resource1.group,
                    instance: resource1.instance
                },
                {
                    type: resource2.type,
                    group: resource2.group,
                    instance: resource2.instance
                }
            ],
            timestamp: Date.now(),
            recommendations: [
                'Check if these resources are intended to be similar',
                'Consider removing one of the resources if they serve the same purpose',
                'If both resources are needed, ensure they are compatible'
            ],
            metadata: {
                similarity,
                lshType,
                resource1Name: resource1.resourceType || 'Unknown',
                resource2Name: resource2.resourceType || 'Unknown',
                resource1Path: resource1.packagePath || 'Unknown',
                resource2Path: resource2.packagePath || 'Unknown'
            },
            confidence: similarity
        };
    }
}
