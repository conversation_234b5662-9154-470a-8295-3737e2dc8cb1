/**
 * Enhanced Worker Pool Implementation using Piscina
 *
 * This module provides an enhanced worker pool for parallel processing of CPU-intensive tasks
 * using the Piscina library, which is built on Node.js worker threads.
 *
 * Features:
 * - Dynamic worker count based on system load
 * - Work stealing for better load balancing
 * - Task prioritization
 * - Detailed performance metrics
 * - Automatic error recovery
 */

import Piscin<PERSON> from 'piscina';
import { cpus } from 'os';
import { join } from 'path';
import { Logger } from '../logging/logger.js';
import { EventEmitter } from 'events';
import os from 'os';

// Create a logger for this module
const logger = new Logger('WorkerPool');

// Task types that can be processed by workers
export enum TaskType {
    RESOURCE_ANALYSIS = 'resourceAnalysis',
    CONFLICT_DETECTION = 'conflictDetection',
    METADATA_EXTRACTION = 'metadataExtraction',
    SIMDATA_PARSING = 'simdataParsing',
    TUNING_PARSING = 'tuningParsing',
    SCRIPT_ANALYSIS = 'scriptAnalysis',
    GENERIC = 'generic',
    BATCH_PROCESSING = 'batchProcessing',
    CONTENT_STORAGE = 'contentStorage',
    SIGNATURE_CALCULATION = 'signatureCalculation',
    DEEP_COMPARISON = 'deepComparison'
}

// Task priority levels
export enum TaskPriority {
    LOWEST = -1,
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3,
    EMERGENCY = 4
}

// Task interface
export interface Task<T = any, R = any> {
    id: string;
    type: TaskType;
    data: T;
    priority: TaskPriority;
    createdAt: number;
    workerId?: number; // Assigned worker ID for work stealing
    retryCount?: number; // Number of times this task has been retried
    maxRetries?: number; // Maximum number of retries allowed
}

// Task result interface
export interface TaskResult<R = any> {
    taskId: string;
    result: R;
    error?: Error;
    duration: number;
    workerId?: number; // Worker that processed the task
    retryCount?: number; // Number of times this task was retried
}

// Worker pool options
export interface WorkerPoolOptions {
    maxThreads?: number;
    minThreads?: number;
    idleTimeout?: number;
    taskTimeout?: number;
    concurrentTasksPerWorker?: number;
    workerPath?: string;
    dynamicThreads?: boolean; // Enable dynamic thread count adjustment
    workStealing?: boolean; // Enable work stealing between workers
    maxRetries?: number; // Maximum number of retries for failed tasks
    monitorInterval?: number; // Interval for monitoring system load in ms
    highLoadThreshold?: number; // CPU load threshold to reduce threads (0-1)
    lowLoadThreshold?: number; // CPU load threshold to increase threads (0-1)
}

// System load information
interface SystemLoad {
    cpuUsage: number; // 0-1
    memoryUsage: number; // 0-1
    timestamp: number;
}

// Default options
const DEFAULT_OPTIONS: WorkerPoolOptions = {
    maxThreads: Math.max(1, cpus().length - 1), // Leave one CPU for the main thread
    minThreads: 1,
    idleTimeout: 60000, // 1 minute
    taskTimeout: 30000, // 30 seconds
    concurrentTasksPerWorker: 1,
    workerPath: join(process.cwd(), 'src', 'utils', 'parallel', 'worker.js'),
    dynamicThreads: true,
    workStealing: true,
    maxRetries: 3,
    monitorInterval: 5000, // 5 seconds
    highLoadThreshold: 0.8, // 80% CPU usage
    lowLoadThreshold: 0.3 // 30% CPU usage
};

/**
 * WorkerPool class for parallel processing
 * Implements the Singleton pattern to ensure only one worker pool exists
 */
export class WorkerPool extends EventEmitter {
    private static instance: WorkerPool;
    private piscina: Piscina;
    private options: WorkerPoolOptions;
    private stats = {
        totalTasks: 0,
        completedTasks: 0,
        failedTasks: 0,
        activeTasks: 0,
        queuedTasks: 0,
        peakActiveTasks: 0,
        retriedTasks: 0,
        taskTypeStats: new Map<TaskType, { total: number, completed: number, failed: number, retried: number }>(),
        workerStats: new Map<number, { tasks: number, completedTasks: number, failedTasks: number, avgDuration: number }>()
    };
    private taskMap = new Map<string, { task: Task, startTime?: number, workerId?: number }>();
    private isInitialized = false;
    private monitorIntervalId?: NodeJS.Timeout;
    private systemLoadHistory: SystemLoad[] = [];
    private MAX_LOAD_HISTORY = 10;
    private workerIdCounter = 0;
    private workerTaskCounts = new Map<number, number>(); // Track tasks per worker for work stealing

    /**
     * Private constructor to enforce Singleton pattern
     * @param options Worker pool options
     */
    private constructor(options: WorkerPoolOptions = {}) {
        super();
        this.setMaxListeners(100); // Set high max listeners for the pool
        this.options = { ...DEFAULT_OPTIONS, ...options };
        logger.info(`Creating worker pool with max threads: ${this.options.maxThreads}`);
    }

    /**
     * Get the WorkerPool instance (Singleton pattern)
     * @param options Worker pool options (only used on first call)
     * @returns The WorkerPool instance
     */
    public static getInstance(options?: WorkerPoolOptions): WorkerPool {
        if (!WorkerPool.instance) {
            WorkerPool.instance = new WorkerPool(options);
        }
        return WorkerPool.instance;
    }

    /**
     * Initialize the worker pool
     * @returns Promise that resolves when the pool is initialized
     */
    public async initialize(): Promise<void> {
        if (this.isInitialized) {
            logger.warn('Worker pool is already initialized');
            return;
        }

        try {
            this.piscina = new Piscina({
                filename: this.options.workerPath,
                minThreads: this.options.minThreads,
                maxThreads: this.options.maxThreads,
                idleTimeout: this.options.idleTimeout,
                taskTimeout: this.options.taskTimeout,
                concurrentTasksPerWorker: this.options.concurrentTasksPerWorker
            });

            // Set up event listeners
            this.piscina.on('error', (error) => {
                logger.error('Worker pool error:', error);
                this.emit('error', error);
            });

            this.piscina.on('completed', () => {
                this.emit('taskCompleted');
            });

            // Start system load monitoring if dynamic threads are enabled
            if (this.options.dynamicThreads) {
                this.startSystemLoadMonitoring();
            }

            this.isInitialized = true;
            logger.info(`Worker pool initialized with ${this.options.maxThreads} threads`);
        } catch (error) {
            logger.error('Failed to initialize worker pool:', error);
            throw error;
        }
    }

    /**
     * Start monitoring system load for dynamic thread adjustment
     * @private
     */
    private startSystemLoadMonitoring(): void {
        if (this.monitorIntervalId) {
            clearInterval(this.monitorIntervalId);
        }

        this.monitorIntervalId = setInterval(() => {
            this.monitorSystemLoad();
        }, this.options.monitorInterval);

        logger.info(`System load monitoring started (interval: ${this.options.monitorInterval}ms)`);
    }

    /**
     * Monitor system load and adjust thread count if needed
     * @private
     */
    private async monitorSystemLoad(): Promise<void> {
        try {
            // Get current system load
            const load = await this.getSystemLoad();

            // Add to history
            this.systemLoadHistory.push(load);

            // Trim history if needed
            if (this.systemLoadHistory.length > this.MAX_LOAD_HISTORY) {
                this.systemLoadHistory.shift();
            }

            // Calculate average load
            const avgLoad = this.calculateAverageLoad();

            // Adjust thread count based on load
            this.adjustThreadCount(avgLoad);

            // Perform work stealing if enabled
            if (this.options.workStealing) {
                this.performWorkStealing();
            }
        } catch (error) {
            logger.error('Error monitoring system load:', error);
        }
    }

    /**
     * Get current system load
     * @private
     * @returns Promise resolving to system load information
     */
    private async getSystemLoad(): Promise<SystemLoad> {
        // Get CPU usage
        const cpuUsage = await this.getCpuUsage();

        // Get memory usage
        const memUsage = process.memoryUsage();
        const memoryUsage = memUsage.heapUsed / memUsage.heapTotal;

        return {
            cpuUsage,
            memoryUsage,
            timestamp: Date.now()
        };
    }

    /**
     * Get CPU usage (0-1)
     * @private
     * @returns Promise resolving to CPU usage
     */
    private async getCpuUsage(): Promise<number> {
        return new Promise<number>((resolve) => {
            const startUsage = process.cpuUsage();
            const startTime = process.hrtime.bigint();

            // Wait a short time to measure CPU usage
            setTimeout(() => {
                const endUsage = process.cpuUsage(startUsage);
                const endTime = process.hrtime.bigint();

                const elapsedTime = Number(endTime - startTime) / 1e9; // Convert to seconds
                const totalUsage = (endUsage.user + endUsage.system) / 1e6; // Convert to milliseconds

                // Calculate CPU usage as a fraction of total available CPU time
                const cpuCount = os.cpus().length;
                const cpuUsage = totalUsage / (elapsedTime * 1000 * cpuCount);

                resolve(Math.min(1, Math.max(0, cpuUsage)));
            }, 100);
        });
    }

    /**
     * Calculate average system load
     * @private
     * @returns Average system load
     */
    private calculateAverageLoad(): { cpuUsage: number; memoryUsage: number } {
        if (this.systemLoadHistory.length === 0) {
            return { cpuUsage: 0, memoryUsage: 0 };
        }

        const totalCpuUsage = this.systemLoadHistory.reduce((sum, load) => sum + load.cpuUsage, 0);
        const totalMemoryUsage = this.systemLoadHistory.reduce((sum, load) => sum + load.memoryUsage, 0);

        return {
            cpuUsage: totalCpuUsage / this.systemLoadHistory.length,
            memoryUsage: totalMemoryUsage / this.systemLoadHistory.length
        };
    }

    /**
     * Adjust thread count based on system load
     * @private
     * @param avgLoad Average system load
     */
    private adjustThreadCount(avgLoad: { cpuUsage: number; memoryUsage: number }): void {
        if (!this.isInitialized || !this.options.dynamicThreads) {
            return;
        }

        const currentThreads = this.piscina.threads.size;
        const maxThreads = this.options.maxThreads!;
        const minThreads = this.options.minThreads!;

        // High load: reduce threads
        if (avgLoad.cpuUsage > this.options.highLoadThreshold!) {
            if (currentThreads > minThreads) {
                const newThreads = Math.max(minThreads, currentThreads - 1);
                this.piscina.options.maxThreads = newThreads;
                logger.info(`High CPU load (${(avgLoad.cpuUsage * 100).toFixed(1)}%), reducing threads to ${newThreads}`);
            }
        }
        // Low load: increase threads
        else if (avgLoad.cpuUsage < this.options.lowLoadThreshold! && this.stats.activeTasks > currentThreads) {
            if (currentThreads < maxThreads) {
                const newThreads = Math.min(maxThreads, currentThreads + 1);
                this.piscina.options.maxThreads = newThreads;
                logger.info(`Low CPU load (${(avgLoad.cpuUsage * 100).toFixed(1)}%), increasing threads to ${newThreads}`);
            }
        }
    }

    /**
     * Perform work stealing between workers
     * @private
     */
    private performWorkStealing(): void {
        if (!this.isInitialized || !this.options.workStealing || this.taskMap.size === 0) {
            return;
        }

        // Find overloaded and underloaded workers
        const workerLoads = Array.from(this.workerTaskCounts.entries());
        if (workerLoads.length < 2) {
            return; // Need at least 2 workers for stealing
        }

        // Sort by load (descending)
        workerLoads.sort((a, b) => b[1] - a[1]);

        const mostLoadedWorker = workerLoads[0];
        const leastLoadedWorker = workerLoads[workerLoads.length - 1];

        // Check if load difference is significant
        if (mostLoadedWorker[1] - leastLoadedWorker[1] > 2) {
            // Find a task from the most loaded worker
            for (const [taskId, taskInfo] of this.taskMap.entries()) {
                if (taskInfo.workerId === mostLoadedWorker[0]) {
                    // Reassign to least loaded worker
                    taskInfo.workerId = leastLoadedWorker[0];

                    // Update worker task counts
                    this.workerTaskCounts.set(mostLoadedWorker[0], mostLoadedWorker[1] - 1);
                    this.workerTaskCounts.set(leastLoadedWorker[0], leastLoadedWorker[1] + 1);

                    logger.debug(`Work stealing: Reassigned task ${taskId} from worker ${mostLoadedWorker[0]} to ${leastLoadedWorker[0]}`);
                    break;
                }
            }
        }
    }

    /**
     * Submit a task to the worker pool
     * @param task The task to submit
     * @returns Promise that resolves with the task result
     */
    public async submitTask<T, R>(task: Task<T, R>): Promise<TaskResult<R>> {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Initialize task retry count if not set
        if (task.retryCount === undefined) {
            task.retryCount = 0;
        }

        // Initialize max retries if not set
        if (task.maxRetries === undefined) {
            task.maxRetries = this.options.maxRetries;
        }

        // Assign a worker ID for work stealing
        const workerId = this.assignWorker();
        task.workerId = workerId;

        // Update stats
        this.stats.totalTasks++;
        this.stats.activeTasks++;
        this.stats.peakActiveTasks = Math.max(this.stats.peakActiveTasks, this.stats.activeTasks);

        // Update task type stats
        if (!this.stats.taskTypeStats.has(task.type)) {
            this.stats.taskTypeStats.set(task.type, { total: 0, completed: 0, failed: 0, retried: 0 });
        }
        const typeStats = this.stats.taskTypeStats.get(task.type)!;
        typeStats.total++;

        // Update worker stats
        if (!this.stats.workerStats.has(workerId)) {
            this.stats.workerStats.set(workerId, { tasks: 0, completedTasks: 0, failedTasks: 0, avgDuration: 0 });
        }
        const workerStats = this.stats.workerStats.get(workerId)!;
        workerStats.tasks++;

        // Update worker task count for work stealing
        this.workerTaskCounts.set(workerId, (this.workerTaskCounts.get(workerId) || 0) + 1);

        // Store task in map
        this.taskMap.set(task.id, { task, startTime: Date.now(), workerId });

        try {
            // Run the task in the worker pool
            const startTime = Date.now();
            const result = await this.piscina.run({ task });
            const endTime = Date.now();
            const duration = endTime - startTime;

            // Update stats
            this.stats.completedTasks++;
            this.stats.activeTasks--;
            typeStats.completed++;
            workerStats.completedTasks++;

            // Update worker average duration
            workerStats.avgDuration = (workerStats.avgDuration * (workerStats.completedTasks - 1) + duration) / workerStats.completedTasks;

            // Update worker task count for work stealing
            this.workerTaskCounts.set(workerId, Math.max(0, (this.workerTaskCounts.get(workerId) || 0) - 1));

            // Remove task from map
            this.taskMap.delete(task.id);

            // Create task result
            const taskResult: TaskResult<R> = {
                taskId: task.id,
                result,
                duration,
                workerId,
                retryCount: task.retryCount
            };

            // Emit task completed event
            this.emit('taskCompleted', taskResult);

            return taskResult;
        } catch (error: any) {
            // Update stats
            this.stats.failedTasks++;
            this.stats.activeTasks--;
            typeStats.failed++;
            workerStats.failedTasks++;

            // Update worker task count for work stealing
            this.workerTaskCounts.set(workerId, Math.max(0, (this.workerTaskCounts.get(workerId) || 0) - 1));

            // Check if we should retry the task
            if (task.retryCount < task.maxRetries!) {
                // Increment retry count
                task.retryCount++;

                // Update stats
                this.stats.retriedTasks++;
                typeStats.retried++;

                logger.warn(`Task ${task.id} failed, retrying (${task.retryCount}/${task.maxRetries}): ${error.message || error}`);

                // Retry the task with a different worker if possible
                task.workerId = this.assignWorker(workerId); // Try to assign a different worker

                // Remove from task map (will be re-added in the recursive call)
                this.taskMap.delete(task.id);

                // Retry the task
                return this.submitTask(task);
            }

            // Remove task from map
            this.taskMap.delete(task.id);

            // Create task result with error
            const taskResult: TaskResult<R> = {
                taskId: task.id,
                result: null as any,
                error,
                duration: Date.now() - (this.taskMap.get(task.id)?.startTime || Date.now()),
                workerId,
                retryCount: task.retryCount
            };

            // Emit task failed event
            this.emit('taskFailed', taskResult);

            // Log error
            logger.error(`Task ${task.id} failed after ${task.retryCount} retries:`, error);

            // Rethrow error
            throw error;
        }
    }

    /**
     * Assign a worker to a task
     * @param excludeWorkerId Worker ID to exclude (for retries)
     * @returns Worker ID
     * @private
     */
    private assignWorker(excludeWorkerId?: number): number {
        if (!this.isInitialized) {
            return ++this.workerIdCounter;
        }

        // Get worker loads
        const workerLoads = Array.from(this.workerTaskCounts.entries());

        // If no workers yet, create a new one
        if (workerLoads.length === 0) {
            return ++this.workerIdCounter;
        }

        // Sort by load (ascending)
        workerLoads.sort((a, b) => a[1] - b[1]);

        // Find the least loaded worker that's not excluded
        for (const [workerId, load] of workerLoads) {
            if (workerId !== excludeWorkerId) {
                return workerId;
            }
        }

        // If all workers are excluded, create a new one
        return ++this.workerIdCounter;
    }

    /**
     * Get current worker pool statistics
     * @returns Current worker pool statistics
     */
    public getStats() {
        return {
            ...this.stats,
            taskTypeStats: Object.fromEntries(this.stats.taskTypeStats.entries()),
            workerInfo: {
                size: this.isInitialized ? this.piscina.threads.size : 0,
                maxSize: this.options.maxThreads,
                minSize: this.options.minThreads,
                idleTimeout: this.options.idleTimeout,
                taskTimeout: this.options.taskTimeout
            }
        };
    }

    /**
     * Terminate the worker pool
     * @param force If true, forcefully terminate all workers without waiting for tasks to complete
     * @returns Promise that resolves when the pool is terminated
     */
    public async terminate(force: boolean = false): Promise<void> {
        if (!this.isInitialized) {
            logger.warn('Worker pool is not initialized');
            return;
        }

        try {
            // Stop system load monitoring
            if (this.monitorIntervalId) {
                clearInterval(this.monitorIntervalId);
                this.monitorIntervalId = undefined;
            }

            // Wait for active tasks to complete if not forcing termination
            if (!force && this.stats.activeTasks > 0) {
                logger.info(`Waiting for ${this.stats.activeTasks} active tasks to complete before terminating...`);

                // Create a promise that resolves when all active tasks are completed
                await new Promise<void>((resolve) => {
                    const checkInterval = setInterval(() => {
                        if (this.stats.activeTasks === 0) {
                            clearInterval(checkInterval);
                            resolve();
                        }
                    }, 100);

                    // Set a timeout to prevent hanging indefinitely
                    setTimeout(() => {
                        clearInterval(checkInterval);
                        logger.warn(`Timeout waiting for tasks to complete, ${this.stats.activeTasks} tasks still active`);
                        resolve();
                    }, 30000); // 30 seconds timeout
                });
            }

            // Destroy the worker pool
            await this.piscina.destroy();

            // Clear task map and stats
            this.taskMap.clear();
            this.workerTaskCounts.clear();
            this.systemLoadHistory = [];

            this.isInitialized = false;
            logger.info('Worker pool terminated');
        } catch (error) {
            logger.error('Failed to terminate worker pool:', error);
            throw error;
        }
    }

    /**
     * Reset worker pool statistics
     */
    public resetStats(): void {
        this.stats = {
            totalTasks: 0,
            completedTasks: 0,
            failedTasks: 0,
            activeTasks: 0,
            queuedTasks: 0,
            peakActiveTasks: 0,
            taskTypeStats: new Map<TaskType, { total: number, completed: number, failed: number }>()
        };
    }
}

// Export the WorkerPool class
export default WorkerPool;
