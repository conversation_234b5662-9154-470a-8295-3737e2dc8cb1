import { Logger } from 'winston';
import { ConflictInfo, ConflictSeverity, ConflictType } from '../../../types/conflict/ConflictTypes.js';
import { ResourceInfo } from '../../../types/resource/interfaces.js';

/**
 * Sims 4-specific conflict filter to reduce false positives
 * Based on research from Sims 4 Studio community and modding best practices
 */
export class Sims4ConflictFilter {
    private logger: Logger;

    constructor(logger: Logger) {
        this.logger = logger;
    }

    /**
     * Filter conflicts to remove false positives specific to Sims 4 modding
     * @param conflicts Array of detected conflicts
     * @param resources Map of resource ID to resource info for context
     * @returns Filtered conflicts with false positives removed
     */
    public filterConflicts(
        conflicts: ConflictInfo[],
        resources: Map<string, ResourceInfo>
    ): ConflictInfo[] {
        const originalCount = conflicts.length;
        
        const filtered = conflicts.filter(conflict => {
            // Get resource information for analysis
            const resource1 = this.getResourceFromConflict(conflict, 0, resources);
            const resource2 = this.getResourceFromConflict(conflict, 1, resources);
            
            if (!resource1 || !resource2) {
                return true; // Keep conflict if we can't analyze it
            }

            // Apply Sims 4-specific filtering rules
            if (this.isRecolorMeshRelationship(resource1, resource2)) {
                this.logger.debug(`Filtering recolor-mesh relationship: ${resource1.id} <-> ${resource2.id}`);
                return false;
            }

            if (this.isHarmlessResourceType(resource1.type)) {
                this.logger.debug(`Filtering harmless resource type: ${resource1.type.toString(16)}`);
                return false;
            }

            if (this.isLowImpactConflict(conflict, resource1, resource2)) {
                this.logger.debug(`Filtering low-impact conflict: ${conflict.id}`);
                return false;
            }

            if (this.isDependencyRelationship(resource1, resource2)) {
                this.logger.debug(`Filtering dependency relationship: ${resource1.id} <-> ${resource2.id}`);
                return false;
            }

            return true; // Keep the conflict
        });

        const filteredCount = originalCount - filtered.length;
        this.logger.info(`Sims4ConflictFilter: Removed ${filteredCount} false positives (${((filteredCount / originalCount) * 100).toFixed(1)}%)`);

        return filtered;
    }

    /**
     * Check if two resources represent a recolor-mesh relationship
     * This is a common false positive in Sims 4 conflict detection
     */
    private isRecolorMeshRelationship(resource1: ResourceInfo, resource2: ResourceInfo): boolean {
        // Check for CAS (Create-A-Sim) recolor relationships
        const casTypes = [
            0x034AEECB, // CAS_PART
            0x0354796A, // CAS_MODIFIER
            0x9D1FFBCD, // CAS_PRESET
        ];

        if (casTypes.includes(resource1.type) && casTypes.includes(resource2.type)) {
            // Check if they have the same group but different instances (typical recolor pattern)
            if (resource1.group === resource2.group && resource1.instance !== resource2.instance) {
                return true;
            }
        }

        // Check for object recolor relationships
        const objectTypes = [
            0x319E4F1D, // OBJECT_DEFINITION
            0x515CA4CD, // CATALOG_OBJECT
        ];

        if (objectTypes.includes(resource1.type) && objectTypes.includes(resource2.type)) {
            // Similar group, different instance pattern
            if (resource1.group === resource2.group && resource1.instance !== resource2.instance) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if a resource type rarely causes actual gameplay conflicts
     */
    private isHarmlessResourceType(resourceType: number): boolean {
        const harmlessTypes = [
            0x2E75C764, // THUMBNAIL_IMAGE
            0x2F7D0004, // ICON_IMAGE  
            0x00B2D882, // TEXTURE_IMAGE
            0x0580A2B4, // MATERIAL_DEFINITION (usually decorative)
            0x0166038C, // NAME_MAP (localization, rarely conflicts)
            0x220557DA, // STRING_TABLE (localization)
            0x7672F0C5, // AUDIO_CLIP (background sounds)
        ];

        return harmlessTypes.includes(resourceType);
    }

    /**
     * Check if a conflict is low-impact and likely a false positive
     */
    private isLowImpactConflict(
        conflict: ConflictInfo,
        resource1: ResourceInfo,
        resource2: ResourceInfo
    ): boolean {
        // Low similarity conflicts are often false positives
        const similarity = conflict.metadata?.similarity || 0;
        if (similarity < 0.9 && conflict.severity === ConflictSeverity.LOW) {
            return true;
        }

        // Different package paths with low similarity
        if (resource1.packagePath !== resource2.packagePath && similarity < 0.95) {
            return true;
        }

        // Resources with very different names/purposes
        if (this.haveDifferentPurposes(resource1, resource2)) {
            return true;
        }

        return false;
    }

    /**
     * Check if two resources have a dependency relationship (should work together)
     */
    private isDependencyRelationship(resource1: ResourceInfo, resource2: ResourceInfo): boolean {
        // Check for mesh-texture dependencies
        const meshTypes = [0x01661233, 0x015A1849]; // MESH types
        const textureTypes = [0x00B2D882, 0x2E75C764]; // TEXTURE types

        if ((meshTypes.includes(resource1.type) && textureTypes.includes(resource2.type)) ||
            (textureTypes.includes(resource1.type) && meshTypes.includes(resource2.type))) {
            return true;
        }

        // Check for tuning-script dependencies
        const tuningTypes = [0x62E94D38, 0x545AC67A]; // TUNING types
        const scriptTypes = [0x6017E896]; // SCRIPT types

        if ((tuningTypes.includes(resource1.type) && scriptTypes.includes(resource2.type)) ||
            (scriptTypes.includes(resource1.type) && tuningTypes.includes(resource2.type))) {
            return true;
        }

        return false;
    }

    /**
     * Check if two resources serve different purposes (less likely to conflict)
     */
    private haveDifferentPurposes(resource1: ResourceInfo, resource2: ResourceInfo): boolean {
        // Get resource categories
        const category1 = this.getResourceCategory(resource1.type);
        const category2 = this.getResourceCategory(resource2.type);

        // Different categories are less likely to conflict meaningfully
        return category1 !== category2;
    }

    /**
     * Categorize resource types for purpose analysis
     */
    private getResourceCategory(resourceType: number): string {
        // CAS (Create-A-Sim) resources
        if ([0x034AEECB, 0x0354796A, 0x9D1FFBCD].includes(resourceType)) {
            return 'cas';
        }

        // Build/Buy objects
        if ([0x319E4F1D, 0x515CA4CD, 0x0580A2B4].includes(resourceType)) {
            return 'object';
        }

        // Gameplay/Tuning
        if ([0x62E94D38, 0x545AC67A, 0x6017E896].includes(resourceType)) {
            return 'gameplay';
        }

        // Visual/Audio
        if ([0x00B2D882, 0x2E75C764, 0x7672F0C5].includes(resourceType)) {
            return 'media';
        }

        // Localization
        if ([0x0166038C, 0x220557DA].includes(resourceType)) {
            return 'localization';
        }

        return 'other';
    }

    /**
     * Extract resource info from conflict
     */
    private getResourceFromConflict(
        conflict: ConflictInfo,
        index: number,
        resources: Map<string, ResourceInfo>
    ): ResourceInfo | null {
        if (!conflict.affectedResources || conflict.affectedResources.length <= index) {
            return null;
        }

        const resourceKey = conflict.affectedResources[index];
        const resourceId = `${resourceKey.type.toString(16)}_${resourceKey.group.toString(16)}_${resourceKey.instance.toString(16)}`;
        
        return resources.get(resourceId) || null;
    }
}
